Default to using Groq. Each large language model has its pros and cons, and the current services provided are as follows:  
- **Groq**: The fastest, completing tasks almost instantly, but with the least effective results. It is recommended to apply for your own API Key to avoid rate limiting due to excessive usage.  
- **OpenAI**: The most popular but slower in speed, with less effective tarot card interpretations.  
- **Gemini**: The slowest but offers the best interpretation results.