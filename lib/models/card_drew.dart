import 'package:flutter/material.dart';

import '../constants/waite_tarot.dart';

enum DrewModeEnum {
  flipcard(Icon(Icons.star_outlined)),
  random(Icon(Icons.shuffle)),
  choice(Icon(Icons.swipe_up_outlined));

  final Widget icon;
  const DrewModeEnum(this.icon);
}

class CardDrew {
  String cardId = "";
  bool reversed = false;
  String? title;
  int rating = 0;
  String? reading;

  CardDrew({
    required this.cardId,
    required this.reversed,
  });

  String get fullname {
    var ret = WaiteTarot.cards[cardId]!;
    if (reversed) ret += "(reversed)";
    return ret;
  }

  @override
  String toString() {
    return "$fullname($rating)";
  }
}
