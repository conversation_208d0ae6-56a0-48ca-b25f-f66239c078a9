import 'package:adv_tarot/models/card_drew.dart';
import 'package:adv_tarot/models/model_clone.dart';
import 'package:hive_ce/hive.dart';

import '../services/hive_service.dart';
import 'channel_enum.dart';

class TarotModel extends HiveObject implements ModelClone {
  DateTime createAt = DateTime.now();
  DateTime updateAt = DateTime.now();
  ChannelEnum channel = ChannelEnum.divine;
  String question = "";
  String? options;
  String? reading;
  String? result;
  String? summary;
  String? comment;
  String? yesno;
  DrewModeEnum drewMode = DrewModeEnum.flipcard;
  List<CardDrew> cards = [];

  @override
  Future<void> save() async {
    if (isInBox == false) {
      await HiveService.I.box.add(this);
    } else {
      updateAt = DateTime.now();
      await super.save();
    }
  }

  @override
  TarotModel clone() {
    return TarotModel()
      ..channel = channel
      ..question = question
      ..options = options;
  }
}
