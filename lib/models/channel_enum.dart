import 'package:flutter/material.dart';

enum ChannelEnum {
  divine(
    "Divination",
    "assets/images/channels/divine.jpg",
    Color(0xFFFFAF87),
    numCards: 5,
  ),
  journal(
    "Journal",
    "assets/images/channels/pickcard.jpg",
    Color(0xFFC0C0C0),
    numCards: 4,
  ),
  rating(
    "Ratings",
    "assets/images/channels/rating.jpg",
    Color(0xFFED6A5E),
    numCards: -1,
  ),
  decision(
    "Decision",
    "assets/images/channels/decision.jpg",
    Color(0xFF4CE0B3),
    numCards: 1,
  ),
  ;

  final String title;
  final String imagePath;
  final Color color;
  final int numCards;
  const ChannelEnum(
    this.title,
    this.imagePath,
    this.color, {
    required this.numCards,
  });
}
