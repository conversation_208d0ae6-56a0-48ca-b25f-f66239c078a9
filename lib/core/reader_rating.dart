part of 'reader_base.dart';

class ReaderRating extends ReaderBase {
  ReaderRating();

  @override
  String get feature => """
難以在多個選項中抉擇？評分模式為每個選項抽取一張塔羅牌，解析優勢與潛在可能。  
透過直覺性的評分參考，幫助你權衡選擇，找到最適合的方向。  
""";

  @override
  String get systemPrompt => """
# 評分塔羅占卜

## 角色扮演

先分析問題是屬於哪個領域，你就是那個領域裡面的專家。
你將扮演一位塔羅解讀師，協助使用者分析每個選項的塔羅牌，並提供評分（0-10分）及解讀，幫助使用者理解每個選項的優勢。每個選項對應一張塔羅牌，根據其意義給出評分和解讀。

## 塔羅占卜規則
1. 每個題目有多個選項，對應每個選項抽一張塔羅牌。
2. 每張塔羅牌的評分範圍為 0-10，評分反映選項的優勢。
3. 解讀以 **JSON 格式** 輸出，包含每個選項的名稱、評分與解讀。

### 解讀範例
```json
[
  {
    "option": "(選項1)",
    "rating": "(分數1)",
    "reading": "(解讀1)"
  },
  {
    "option": "(選項2)",
    "rating": "(分數2)",
    "reading": "(解讀2)"
  }
]
```
""";

  @override
  String userPrompt(TarotModel model, {String? prefixPrompt}) {
    String userPrompt = "解讀以下內容:\n\n## ${model.question}\n\n";
    userPrompt += "\n\n## 選項\n\n";
    for (var card in model.cards) {
      userPrompt += "- ${card.title}: ${card.fullname}\n";
    }
    // 增加前置提示詞
    if (prefixPrompt != null) {
      userPrompt = "$prefixPrompt\n\n$userPrompt";
    }
    return userPrompt;
  }

  @override
  void decode(TarotModel model, String? response) {
    if (response == null) return;
    final json = jsonDecode(getJsonInner(response));
    for (var i = 0; i < model.cards.length; i++) {
      model.cards[i].rating = int.parse("${json[i]["rating"]}");
      model.cards[i].reading = json[i]["reading"];
    }
    final cards = model.cards;
    cards.sort((a, b) => a.rating.compareTo(b.rating) * -1);
  }
}
