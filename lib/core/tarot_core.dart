import '../models/tarot_model.dart';

class TarotCore {
  static String cardCount(TarotModel model) {
    // 計算各種塔羅牌分類的數量
    final cateCount = {"M": 0, "C": 0, "S": 0, "P": 0, "W": 0};
    for (var card in model.cards) {
      final category = card.cardId[0]; // 提取首字母代表的分類
      if (cateCount.containsKey(category)) {
        cateCount[category] = cateCount[category]! + 1;
      }
    }
    final cateNames = {"M": "大阿卡那", "C": "杯", "S": "劍", "P": "幣", "W": "杖", "Court": "宮廷牌", "Rev": "逆位"};
    // 計算宮廷牌的數量
    final courtCount = model.cards.where((card) => card.cardId.substring(1, 2) == "C").length;
    cateCount["Court"] = courtCount;
    final revCount = model.cards.where((e) => e.reversed).length;
    cateCount["Rev"] = revCount;
    var prompt = "\n## 牌種統計:\n";
    for (var e in cateCount.entries) {
      prompt += "${cateNames[e.key]}:${e.value},";
    }
    return prompt;
  }

  /// 加強問題選項提示詞
  static String enhancePrompt = """# 強化塔羅占卜問題
## 規則
請根據用戶輸入的問題，生成五個更具體的問題選項，以幫助塔羅 AI 提供更準確的解讀。

- 確保每個選項都能引導更清晰的占卜方向。
- 可從短期發展、選擇決策、影響因素、建議方向、趨勢預測等不同角度來設計問題。
- 問題應該以不需詳細了解個人狀況即可回答的方式，且能用塔羅牌解讀的方式表述。
- 保持語氣自然、符合日常提問習慣。
- 請使用markdown格式進行回答，不要在回答前加入任何引言或標題，範例如下：
## 範例
- 如何在媒體推廣「AI塔羅大師」時，確保品牌形象與用戶信任？  
- 在推廣「AI塔羅大師」時，應該如何避免觸犯法律或道德規範？  
- 推廣「AI塔羅大師」時，哪些媒體平台最能吸引目標用戶？  
- 如何在媒體推廣中平衡塔羅占卜的神秘感與科技產品的科學感？  
- 推廣「AI塔羅大師」時，如何預防用戶對AI占卜準確性的負面反應？
""";

  static String makeDetailPrompt = """
先分析問題是屬於哪個領域，你就是那個領域裡面的專家，
請根據塔羅牌的占卜解讀結果，提供五個可能的後續提問選項，幫助用戶深入探索問題的細節。

- 每個問題應該圍繞「可能的行動方案」、「影響因素」、「潛在變數」、「心態調整」、「未來發展」等不同角度。
- 保持語氣自然，讓問題符合用戶的思考習慣。
- 請使用markdown格式進行回答，不要在回答前加入任何引言或標題，範例如下：
## 範例
- 如何在媒體推廣「AI塔羅大師」時，確保品牌形象與用戶信任？
- 在推廣「AI塔羅大師」時，應該如何避免觸犯法律或道德規範？
- 推廣「AI塔羅大師」時，哪些媒體平台最能吸引目標用戶？
- 如何在媒體推廣中平衡塔羅占卜的神秘感與科技產品的科學感？
- 推廣「AI塔羅大師」時，如何預防用戶對AI占卜準確性的負面反應？
""";

  /// 系統提示詞
  static String get systemPrompt => """你是一位專業的塔羅占卜師，擁有豐富的塔羅牌解讀經驗。
請根據用戶的問題和抽到的塔羅牌，提供詳細、準確且有幫助的解讀。

解讀時請注意：
1. 結合牌面的傳統含義和用戶的具體問題
2. 考慮牌面的正逆位置
3. 分析牌與牌之間的關聯性
4. 提供實用的建議和指導
5. 保持專業且溫和的語調""";

  /// 獲取用戶提示詞
  static String getUserPrompt(TarotModel model) {
    String prompt = "問題：${model.question}\n\n";
    prompt += "抽到的塔羅牌：\n";

    for (int i = 0; i < model.cards.length; i++) {
      final card = model.cards[i];
      final position = model.cards.length == 1 ? "單張牌" : "第${i + 1}張牌";
      final orientation = card.reversed ? "逆位" : "正位";
      prompt += "$position：${card.cardId} ($orientation)\n";
    }

    prompt += cardCount(model);
    return prompt;
  }
}
