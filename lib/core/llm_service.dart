import 'package:wu_llm/wu_llm.dart';

import '../models/tarot_model.dart';
import '../providers/setting_controller.dart';
import 'tarot_core.dart';

/// LLM 服務類別
class LlmService {
  /// 加強問題選項提示詞
  static String get enhancePrompt => TarotCore.enhancePrompt;

  /// 獲取提示詞
  static (String systemPrompt, String userPrompt) getPrompt2(TarotModel model) {
    final systemPrompt = TarotCore.systemPrompt;
    final userPrompt = TarotCore.getUserPrompt(model);
    return (systemPrompt, userPrompt);
  }

  /// 解碼回應
  static void decode(TarotModel model, String response) {
    // 簡單的解碼邏輯，將回應存儲到模型中
    model.result = response;
  }
}

/// LLM 選擇類別
class LlmChoice {
  static final LlmChoice _instance = LlmChoice._internal();
  static LlmChoice get I => _instance;

  LlmChoice._internal();

  /// 獲取當前設定的 LLM 服務
  LlmBase get _currentService {
    final settingController = Get.find<SettingController>();
    return settingController.llmService;
  }

  /// 獲取可用模型列表
  Future<List<String>> models() async {
    final settingController = Get.find<SettingController>();
    return await settingController.getModels();
  }

  /// 非流式文本生成
  Future<String?> futureText({
    required String userPrompt,
    String? systemPrompt,
  }) async {
    final settingController = Get.find<SettingController>();
    final service = _currentService;

    final messages = <LlmMessage>[];
    if (systemPrompt != null && systemPrompt.isNotEmpty) {
      messages.add(LlmMessage(role: LlmRole.system, content: systemPrompt));
    }
    messages.add(LlmMessage(role: LlmRole.user, content: userPrompt));

    return await service.futureText(
      apiKey: settingController.apiKey ?? '',
      modelName: settingController.modelName ?? service.recommandModels.first,
      messages: messages,
    );
  }

  /// 流式文本生成
  Stream<String?> streamText({
    required String userPrompt,
    String? systemPrompt,
  }) {
    final settingController = Get.find<SettingController>();
    final service = _currentService;

    final messages = <LlmMessage>[];
    if (systemPrompt != null && systemPrompt.isNotEmpty) {
      messages.add(LlmMessage(role: LlmRole.system, content: systemPrompt));
    }
    messages.add(LlmMessage(role: LlmRole.user, content: userPrompt));

    return service.streamText(
      apiKey: settingController.apiKey ?? '',
      modelName: settingController.modelName ?? service.recommandModels.first,
      messages: messages,
    );
  }
}

/// LLM Provider 類別（用於設定頁面）
class LlmProvider extends GetxController {
  String? llmModel;

  @override
  void onInit() {
    super.onInit();
    final settingController = Get.find<SettingController>();
    llmModel = settingController.modelName;
  }

  void updateModel(String? model) {
    llmModel = model;
    final settingController = Get.find<SettingController>();
    settingController.modelName = model;
    update();
  }
}

/// Groq 服務的單例實現（用於向後兼容）
class LlmGroq {
  static final LlmGroq _instance = LlmGroq._internal();
  static LlmGroq get I => _instance;

  LlmGroq._internal();

  String? apikey;
  String? modelName;

  Future<String?> futureText({
    required String userPrompt,
    String? systemPrompt,
  }) async {
    final service = LlmBase.getService("Groq");

    final messages = <LlmMessage>[];
    if (systemPrompt != null && systemPrompt.isNotEmpty) {
      messages.add(LlmMessage(role: LlmRole.system, content: systemPrompt));
    }
    messages.add(LlmMessage(role: LlmRole.user, content: userPrompt));

    return await service.futureText(
      apiKey: apikey ?? '',
      modelName: modelName ?? 'llama-3.3-70b-specdec',
      messages: messages,
    );
  }
}

/// 常數定義
const String groqApiKey = ""; // 需要設定實際的 API Key
