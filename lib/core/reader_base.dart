import 'dart:convert';

import 'package:adv_tarot/models/card_drew.dart';
import 'package:adv_tarot/models/tarot_model.dart';

import '../models/channel_enum.dart';
import 'tarot_core.dart';

part 'reader_decision.dart';
part 'reader_divine.dart';
part 'reader_journal.dart';
part 'reader_rating.dart';

abstract class ReaderBase {
  ReaderBase();

  /// 解讀方式的特點
  String get feature;

  /// 系統提示詞
  String get systemPrompt;

  /// 使用者提示詞
  String userPrompt(TarotModel model, {String? prefixPrompt});

  /// 解碼回應結果並存入模型
  void decode(TarotModel model, String? response);

  /// 取得解讀方式
  static ReaderBase getReader(ChannelEnum mode) {
    return switch (mode) {
      ChannelEnum.decision => ReaderDecision(),
      ChannelEnum.journal => ReaderJournal(),
      ChannelEnum.rating => ReaderRating(),
      ChannelEnum.divine => ReaderDivine(),
    };
  }

  /// 取出 摘要 內容
  String getSummary(String response) {
    final pattern = RegExp(r'## 摘要\n([\s\S]*?)(?=\n##|\Z)'); // 捕獲摘要內容
    final match = pattern.firstMatch(response); // 尋找匹配的部分
    final ret = match?.group(1) ?? response; // 取出群組 1 或原始字串
    return ret.replaceAll('\n', '').trim();
  }

  /// 取出 markdown 內容
  String getMarkdownInner(String response) {
    final pattern = RegExp(r'```markdown\n([\s\S]*?)```'); // 捕獲 markdown 內容
    final match = pattern.firstMatch(response); // 尋找匹配的部分
    return match?.group(1) ?? response; // 取出群組 1 或原始字串
  }

  /// 取出 JSON 內容
  String getJsonInner(String response) {
    final pattern = RegExp(r'```json\n([\s\S]*?)```'); // 捕獲 JSON 內容
    final match = pattern.firstMatch(response); // 尋找匹配的部分
    return match?.group(1) ?? response; // 取出群組 1 或原始字串
  }

  /// 移除<think></think>之間的內容
  String removeThink(String response) {
    return response.replaceAll(RegExp(r'<think>[\s\S]*?</think>'), '');
  }
}
