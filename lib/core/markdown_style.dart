import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:get/get.dart';

MarkdownStyleSheet markdownStyle() {
  final colorScheme = Get.theme.colorScheme;
  final headerStyle = TextStyle(
    color: colorScheme.secondary,
    fontWeight: FontWeight.bold,
  );
  return MarkdownStyleSheet(
    h1: headerStyle.copyWith(fontSize: 20),
    h2: headerStyle.copyWith(fontSize: 18),
    h3: headerStyle.copyWith(fontSize: 16),
    h4: headerStyle.copyWith(fontSize: 14),
    h5: headerStyle.copyWith(fontSize: 12),
    h6: headerStyle.copyWith(fontSize: 10),
  );
}
