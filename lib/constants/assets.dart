import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class Assets {
  static kImageTarot({double width = 20.0}) => Image.asset(
        "assets/images/icons/tarot.png",
        width: width,
      );
  static kImageGpt({double width = 20.0}) => Image.asset(
        "assets/images/icons/gpt.png",
        width: width,
      );
  static kImageMajor({double width = 20.0}) => Image.asset(
        "assets/images/icons/major.png",
        width: width,
      );

  static const imageSaving = Icon(Icons.savings_outlined);
  static const imageCoin = Icon(Icons.paid_outlined);

  static imageAiCoin() => Image.asset("assets/images/ai_coin.jpg");

  static imageShareImage({double width = 32.0, ColorFilter? colorFilter}) => SvgPicture.asset(
        "assets/images/icons/share_image.svg",
        width: width,
        colorFilter: colorFilter,
      );
  static imageShareText({double width = 32.0, ColorFilter? colorFilter}) => SvgPicture.asset(
        "assets/images/icons/share_text.svg",
        width: width,
        colorFilter: colorFilter,
      );
}
