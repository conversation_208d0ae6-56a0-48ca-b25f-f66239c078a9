import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:get/get.dart';

import '../providers/setting_provider.dart';

class DocViewScr extends StatelessWidget {
  final String docPath;
  const DocViewScr({super.key, required this.docPath});

  @override
  Widget build(BuildContext context) {
    final settingProvider = Get.find<SettingProvider>();

    return FutureBuilder(
      future: settingProvider.loadDoc(docPath),
      builder: (_, snapshot) {
        if (snapshot.connectionState != ConnectionState.done) {
          return const Center(child: CircularProgressIndicator());
        }
        return SingleChildScrollView(
          padding: const EdgeInsets.all(12.0),
          child: MarkdownBody(
            data: snapshot.data.toString(),
          ),
        );
      },
    );
  }
}
