import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive_ce_flutter/hive_flutter.dart';
import 'package:grouped_list/grouped_list.dart';
import 'package:wu_core/wu_extensions.dart';
import 'package:wu_core/widgets/divin_tile.dart';

import '../widgets/core_widgets.dart';

import '../constants/waite_tarot.dart';
import '../models/channel_enum.dart';
import '../models/tarot_model.dart';
import '../pages/ch_edit_page.dart';
import '../pages/ch_view_page.dart';
import '../services/hive_service.dart';

class ChListScr extends StatelessWidget {
  final ChannelEnum channel;
  const ChListScr({
    super.key,
    required this.channel,
  });

  @override
  Widget build(BuildContext context) {
    return ConstrainedBox(
      constraints: const BoxConstraints(maxWidth: 600),
      child: ValueListenableBuilder(
        valueListenable: HiveService.I.box.listenable(),
        builder: (context, box, widget) {
          final items = box.values.toList().where((e) => e.channel == channel).toList();
          return buildList(items);
        },
      ),
    );
  }

  Widget buildList(List<TarotModel> items) {
    final dateFormat = DateFormat("yyyy/MM/dd");
    return Builder(builder: (context) {
      return GroupedListView(
        elements: items,
        sort: true,
        order: GroupedListOrder.DESC,
        groupBy: (TarotModel item) => item.createAt.date(),
        groupHeaderBuilder: (TarotModel item) => DateHeader(
          date: item.createAt,
        ),
        itemBuilder: (context, TarotModel item) => DivinTile(
          leading: CircleAvatar(
            backgroundColor: Theme.of(context).colorScheme.secondary,
            foregroundColor: Theme.of(context).colorScheme.onSecondary,
            child: item.drewMode.icon,
          ),
          deleteText: "Delete".tr,
          dupicateText: "Clone".tr,
          title: Text(item.question.isNotEmpty ? item.question : dateFormat.format(item.createAt)),
          subtitle: buildSubtitle(item),
          onDelete: () async => await HiveService.I.box.delete(item.key),
          onClone: () => Get.to(() => ChEditPage(model: item.clone())),
          onTap: () => Get.to(() => ChViewPage(model: item)),
          onLongPress: () => Get.to(() => ChEditPage(model: item.clone())),
        ),
      );
    });
  }

  Widget buildSubtitle(TarotModel item) {
    final cards = item.cards;
    switch (channel) {
      case ChannelEnum.divine:
        return item.summary == null
            ? Wrap(
                spacing: 4,
                children: List.generate(
                    cards.length,
                    (index) => WaiteTarot.image(
                          cards[index].cardId,
                          cards[index].reversed,
                          width: 40,
                        )),
              )
            : Text(item.summary!, maxLines: 3, overflow: TextOverflow.ellipsis);

      case ChannelEnum.decision:
        return Row(children: [
          WaiteTarot.image(
            cards[0].cardId,
            cards[0].reversed,
            width: 40,
          ),
          SizedBox(width: 8),
          Builder(builder: (context) {
            return Text(
              item.yesno ?? "",
              style: Theme.of(context).textTheme.titleMedium,
            );
          }),
        ]);
      case ChannelEnum.rating:
        final card = item.cards[0];
        return Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              children: [
                WaiteTarot.image(
                  card.cardId,
                  card.reversed,
                  width: 40,
                ),
                Text(card.rating.toString()),
              ],
            ),
            SizedBox(width: 8),
            Expanded(child: Text(card.title ?? "", maxLines: 3, overflow: TextOverflow.ellipsis)),
          ],
        );
      case ChannelEnum.journal:
        return item.summary == null
            ? Wrap(
                spacing: 4,
                children: List.generate(
                    cards.length,
                    (index) => WaiteTarot.image(
                          cards[index].cardId,
                          cards[index].reversed,
                          width: 40,
                        )),
              )
            : Text(item.summary!);
    }
  }
}
