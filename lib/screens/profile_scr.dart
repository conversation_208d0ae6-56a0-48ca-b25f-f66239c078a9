import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';

class ProfileScr extends StatelessWidget {
  final GlobalKey<FormBuilderState> formKey;
  const ProfileScr({super.key, required this.formKey});

  @override
  Widget build(BuildContext context) {
    return Form(
      key: form<PERSON><PERSON>,
      child: <PERSON>umn(
        children: [
          buildBirthdayField(),
          buildBirthPlaceField(),
        ],
      ),
    );
  }

  Widget buildBirthdayField() {
    return FormBuilderDateTimePicker(
      name: 'birthday',
      decoration: const InputDecoration(labelText: 'Birthday'),
      inputType: InputType.both,
      initialValue: DateTime.now(),
    );
  }

  Widget buildBirthPlaceField() {
    return FormBuilderTextField(
      name: 'birthplace',
      decoration: const InputDecoration(labelText: 'Birth Place'),
    );
  }
}
