import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../pages/doc_view_page.dart';
import '../providers/setting_provider.dart';

class DocInfo {
  final String title;
  final String path;

  DocInfo({required this.title, required this.path});
}

final docList = [
  DocInfo(title: 'Your Personal Divination Consultant', path: 'readme.md'),
  DocInfo(title: 'Your Personal Tarot Advisor', path: 'limitations.md'),
  DocInfo(title: 'Dynamic Key Card Method', path: 'no_spread.md'),
  DocInfo(title: 'The Key to Tarot Reading', path: 'q_tech.md'),
];

class DocListScr extends StatelessWidget {
  const DocListScr({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final setting = Get.find<SettingProvider>();
    return FutureBuilder<Object>(
        future: setting.loadDoc("doc_list.json"),
        builder: (context, snapshot) {
          if (snapshot.connectionState != ConnectionState.done) {
            return const Center(child: CircularProgressIndicator());
          }
          final json = jsonDecode(snapshot.data as String);
          final titles = List.from(json);
          return ListView.builder(
            itemCount: docList.length,
            itemBuilder: (context, index) {
              final doc = docList[index];
              return Card(
                color: colorScheme.secondaryContainer,
                elevation: 4.0,
                child: ListTile(
                  title: Text(
                    titles[index],
                    style: TextStyle(color: colorScheme.onSecondaryContainer),
                  ),
                  onTap: () {
                    Get.to(() => DocViewPage(
                          docPath: doc.path,
                        ));
                  },
                ),
              );
            },
          );
        });
  }
}
