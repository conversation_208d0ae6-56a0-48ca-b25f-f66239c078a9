import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wu_admob/wu_admob.dart';

import '../models/channel_enum.dart';
import '../pages/ch_list_page.dart';
import '../providers/setting_provider.dart';
import '../widgets/core_widgets.dart';

class ChannelScr extends StatelessWidget {
  const ChannelScr({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SettingProvider>(builder: (provider) {
      return DeviceBuilder(builder: (context, con, deviceType, platform) {
        // mobile
        if (deviceType == DeviceType.mobile) {
          return SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                spacing: 8.0,
                children: [
                  ChargeTile(
                    youhaveText: "你有：".tr,
                    watchAdText: "獲得AI幣".tr,
                  ), // 手機專用看廣告儲值
                  ...List.generate(ChannelEnum.values.length, (index) {
                    final (title, description) = provider.channels[index];
                    return buildChannelTile(ChannelEnum.values[index], title, description);
                  })
                ],
              ),
            ),
          );
        }

        // 根據畫面寬度決定每行顯示數量
        final colCount = switch (deviceType) {
          DeviceType.tablet => 2,
          DeviceType.desktop => 2,
          DeviceType.largeScreen => 3,
          _ => 1,
        };

        final chCount = ChannelEnum.values.length;
        final runSpacing = 8.0;
        final spacing = 8.0;
        final tileWidth = (con.maxWidth - spacing * 2 - spacing * (colCount - 1)) / colCount;
        return SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: runSpacing, horizontal: spacing),
            child: Wrap(
              runSpacing: runSpacing,
              spacing: spacing,
              children: [
                ...List.generate(chCount, (index) {
                  final (title, description) = provider.channels[index];
                  return SizedBox(
                    width: tileWidth,
                    height: 160,
                    child: buildChannelTile(ChannelEnum.values[index], title, description),
                  );
                }),
                // 以後增加桌機專用功能
              ],
            ),
          ),
        );
      });
    });
  }

  // 寬版, 適用於手機
  Widget buildChannelTile(ChannelEnum channel, String title, String description) {
    final imagePath = "assets/images/channels/${channel.name}.png";
    return ChannelTile(
      color: channel.color,
      leading: Image.asset(imagePath),
      title: Text(title),
      subtitle: Text(description),
      onTap: () => Get.to(() => ChListPage(channel: channel)),
    );
  }

  Widget buildChargeTile() {
    return Builder(
      builder: (context) {
        final colorScheme = Theme.of(context).colorScheme;
        return GetBuilder<RewardController>(
          builder: (rewardCtrl) {
            return Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: colorScheme.outline),
                color: colorScheme.secondaryContainer,
              ),
              clipBehavior: Clip.antiAlias,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Row(
                    spacing: 2,
                    children: [
                      Text(
                        "${"您有：".tr}${rewardCtrl.ticketCount}",
                        style: TextStyle(fontSize: 16, color: colorScheme.onSecondaryContainer),
                      ),
                      Image.asset("assets/images/icons/ai coin.png", height: 20, width: 20),
                    ],
                  ),
                  Spacer(),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      foregroundColor: colorScheme.onPrimary,
                      backgroundColor: colorScheme.primary,
                    ),
                    onPressed: rewardCtrl.loaded ? () async => await rewardCtrl.adShow() : null,
                    child: Text("看廣告充值AI幣".tr),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }
}
