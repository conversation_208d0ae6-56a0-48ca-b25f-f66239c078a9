import 'package:flutter/material.dart';

class ChannelTile extends StatelessWidget {
  final Widget? leading;
  final Widget title;
  final Widget? subtitle;
  final Function()? onTap;
  final Color? color;
  final CrossAxisAlignment rightCrossAxisAlignment;
  const ChannelTile({
    super.key,
    this.leading,
    required this.title,
    this.subtitle,
    this.onTap,
    this.color,
    this.rightCrossAxisAlignment = CrossAxisAlignment.start,
  });

  @override
  Widget build(BuildContext context) {
    final titleStyle = const TextStyle(
      color: Colors.black,
      fontSize: 15.0,
      fontWeight: FontWeight.bold,
    );
    final subtitleStyle = const TextStyle(
      color: Colors.black,
      fontSize: 14.0,
    );

    Widget rightSide = Column(
      spacing: 4.0,
      crossAxisAlignment: rightCrossAxisAlignment, // 文字靠左對齊
      children: [
        DefaultTextStyle(style: titleStyle, child: title),
        if (subtitle != null) DefaultTextStyle(style: subtitleStyle, child: subtitle!),
      ],
    );

    return InkWell(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey, width: 1.0),
          borderRadius: BorderRadius.circular(8.0),
        ),
        clipBehavior: Clip.antiAlias,
        constraints: BoxConstraints(minHeight: 120.0),
        child: IntrinsicHeight(
          child: Row(
            children: [
              if (leading != null)
                Container(
                  width: 100,
                  padding: EdgeInsets.all(8),
                  color: color, // 左邊 Container 的背景顏色
                  child: Center(child: leading), // 圖示置中
                ),
              Expanded(
                child: Container(
                  color: (leading == null) ? color : Colors.white, // 右邊 Container 的背景顏色
                  padding: const EdgeInsets.all(8.0), // 設定內邊距
                  child: rightSide,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
