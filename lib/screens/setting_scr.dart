import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';

import '../constants/languages.dart';
import '../core/llm_service.dart';
import '../providers/setting_provider.dart';
import '../services/hive_service.dart';

class SettingScr extends StatefulWidget {
  const SettingScr({super.key});

  @override
  State<SettingScr> createState() => _SettingScrState();
}

class _SettingScrState extends State<SettingScr> {
  final decoration = InputDecoration(
    border: const OutlineInputBorder(),
    contentPadding: const EdgeInsets.all(10),
  );

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 600),
          child: Column(
            spacing: 16,
            children: [
              buildReadingLanguage(),
              buildLLMModel(),
              SizedBox(width: double.infinity, child: buildClearHistory()),
              SizedBox(width: double.infinity, child: buildSupportTile()),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildReadingLanguage() {
    return FormBuilderDropdown(
      name: "ReadingLanguage",
      initialValue: Get.find<SettingProvider>().readingLanguage,
      decoration: decoration.copyWith(
        labelText: "Reading Language".tr,
        helperText: "Language.Hint".tr,
      ),
      items: LanguageEnum.values
          .map(
            (e) => DropdownMenuItem(value: e, child: Text(e.title)),
          )
          .toList(),
      onChanged: (value) {
        final provider = Get.find<SettingProvider>();
        provider.readingLanguage = value ?? LanguageEnum.zh;
        provider.save();
        provider.update();
      },
    );
  }

  Widget buildLLMButton(String title, String url) {
    return TextButton(
      child: Text(title),
      onPressed: () => launchUrl(Uri.parse(url)),
    );
  }

  Widget buildLLMModel() {
    return GetBuilder<LlmProvider>(builder: (provider) {
      return FutureBuilder(
          future: LlmChoice.I.models(),
          builder: (context, snapshot) {
            if (snapshot.connectionState != ConnectionState.done) {
              return const CircularProgressIndicator();
            }
            final models = snapshot.data ?? [];
            models.sort((a, b) => a.compareTo(b));
            return FormBuilderDropdown(
              name: "LLMModel",
              initialValue: provider.llmModel,
              decoration: decoration.copyWith(labelText: "LLM Model".tr),
              items: models.map((e) => DropdownMenuItem(value: e, child: Text(e))).toList(),
              onChanged: (value) {
                provider.llmModel = value;
              },
            );
          });
    });
  }

  Widget buildLLMRecommend() {
    final settingProvider = Get.find<SettingProvider>();
    return FutureBuilder(
        future: settingProvider.loadDoc("llm_inter.md"),
        builder: (context, snapshot) {
          if (snapshot.connectionState != ConnectionState.done) {
            return const Center(child: CircularProgressIndicator());
          }
          final md = snapshot.data.toString();
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              MarkdownBody(data: md, selectable: true),
              OverflowBar(
                children: [
                  Text("Obtain API Key:".tr),
                  buildLLMButton("Groq", "https://console.groq.com/login"),
                  buildLLMButton("OpenAI", "https://platform.openai.com/api-keys"),
                  buildLLMButton("Gemini", "https://aistudio.google.com/app/apikey"),
                ],
              ),
            ],
          );
        });
  }

  Widget buildClearHistory() {
    final colorScheme = Theme.of(context).colorScheme;
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        backgroundColor: colorScheme.error,
        foregroundColor: colorScheme.onError,
      ),
      child: Text("Clean History".tr),
      onPressed: () async {
        final result = await Get.dialog<bool>(
          AlertDialog(
            title: Text("Clean.History.title".tr),
            content: Text("Clean.History.content".tr),
            actions: [
              TextButton(
                onPressed: () => Get.back(result: false),
                child: Text("Cancel".tr),
              ),
              TextButton(
                onPressed: () => Get.back(result: true),
                child: Text("OK".tr),
              ),
            ],
          ),
        );

        if (result == true) {
          await HiveService.I.clear();
        }
      },
    );
  }

  Widget buildSupportTile() {
    String? encodeQueryParameters(Map<String, String> params) {
      return params.entries
          .map((MapEntry<String, String> e) => '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
          .join('&');
    }

    final colorScheme = Theme.of(context).colorScheme;
    return ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: colorScheme.error,
          foregroundColor: colorScheme.onError,
        ),
        onPressed: () {
          final Uri emailLaunchUri = Uri(
            scheme: 'mailto',
            path: '<EMAIL>',
            query: encodeQueryParameters(<String, String>{
              'subject': 'AI TAROT MASTER (${Platform.operatingSystem}) Feedback',
            }),
          );

          launchUrl(emailLaunchUri);
        },
        child: Text("Contact Us".tr));
  }
}
