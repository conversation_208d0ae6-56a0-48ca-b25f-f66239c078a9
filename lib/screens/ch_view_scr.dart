import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:screenshot/screenshot.dart';

import 'package:wu_admob/wu_admob.dart';
// import 'package:image/image.dart' as img;

import '../constants/waite_tarot.dart';
import '../models/channel_enum.dart';
import '../models/tarot_model.dart';
import '../pages/comment_page.dart';
import '../widgets/cards_field/cards_field.dart';

class ChViewScr extends StatefulWidget {
  final TarotModel model;
  const ChViewScr({super.key, required this.model});

  @override
  State<ChViewScr> createState() => _ChViewScrState();
}

class _ChViewScrState extends State<ChViewScr> {
  @override
  Widget build(BuildContext context) {
    var md = "## ${"Question".tr}\n\n${widget.model.question}\n\n";
    final body = switch (widget.model.channel) {
      ChannelEnum.decision => buildDecisionBody(md),
      ChannelEnum.divine => buildDivineBody(md),
      ChannelEnum.rating => buildRatingBody(md),
      ChannelEnum.journal => buildJournalBody(md),
    };

    return Stack(
      children: [
        body,
        Positioned(
          top: -8,
          right: 0,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (widget.model.channel == ChannelEnum.journal) buildCommentButton(),
              if (widget.model.channel != ChannelEnum.decision) buildShareButton(),
            ],
          ),
        ),
      ],
    );
  }

  Widget buildShareButton() {
    final isShraed = (widget.model.reading != null) || (widget.model.channel == ChannelEnum.rating);
    final colorScheme = Theme.of(context).colorScheme;
    final foreColor = colorScheme.onSurface;
    return IconButton(
      style: IconButton.styleFrom(
        foregroundColor: foreColor,
      ),
      icon: Icon(Icons.share, color: foreColor),
      onPressed: isShraed ? () async => shareDrewCards() : null,
    );
  }

  void shareDrewCards() async {
    final screenshotController = ScreenshotController();
    await screenshotController
        .captureFromWidget(
      Container(
        padding: const EdgeInsets.all(20.0),
        decoration: BoxDecoration(
          color: Colors.white,
        ),
        child: CardRow(
          cards: widget.model.cards,
          height: 60,
          withRating: (widget.model.channel == ChannelEnum.rating),
        ),
      ),
    )
        .then((capturedImage) async {
      shareText();
      final directory = await getApplicationDocumentsDirectory();
      final imagePath = await File('${directory.path}/image.png').create();
      await imagePath.writeAsBytes(capturedImage);
      // TODO: 使用 SharePlus 替代已棄用的 Share
      // await SharePlus.shareXFiles([XFile(imagePath.path)]);
    });
  }

  void shareText() async {
    var markdown = widget.model.question.isNotEmpty ? "## ${"Question".tr}\n\n${widget.model.question}\n\n" : "";
    markdown += "${widget.model.reading ?? ""}\n\n";
    if ((widget.model.channel == ChannelEnum.journal) && (widget.model.comment != null)) {
      markdown += "## ${"Comment".tr}\n\n${widget.model.comment}\n\n";
    } else if (widget.model.channel == ChannelEnum.rating) {
      markdown = "## ${"Question".tr}\n\n${widget.model.question}\n\n";
      for (var card in widget.model.cards) {
        markdown += "### ${card.title} 🃏${card.fullname} ⭐ ${card.rating}\n\n${card.reading ?? ""}\n\n";
      }
    }
    markdown += "🔯AI TAROT MASTER (android)🔯\n\n";
    markdown += "https://play.google.com/store/apps/details?id=com.uncledivin.adv_tarot";
    // 簡單的 Markdown 轉換，移除 Markdown 格式
    final fbText = markdown
        .replaceAll(RegExp(r'\*\*(.*?)\*\*'), r'$1') // 移除粗體
        .replaceAll(RegExp(r'\*(.*?)\*'), r'$1') // 移除斜體
        .replaceAll(RegExp(r'#{1,6}\s*'), '') // 移除標題
        .replaceAll(RegExp(r'\[([^\]]+)\]\([^\)]+\)'), r'$1'); // 移除連結格式
    Clipboard.setData(ClipboardData(text: fbText));
    Get.snackbar(
      '已複製到剪貼簿',
      '您可以將內容分享到其他地方。',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  Widget buildCommentButton() {
    return IconButton(
      style: IconButton.styleFrom(
        foregroundColor: Theme.of(context).colorScheme.primary,
      ),
      icon: const Icon(Icons.comment),
      onPressed: () async {
        await Get.to(() => CommentPage(model: widget.model));
        setState(() {});
      },
    );
  }

  Widget buildDivineBody(String mdQuestion) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: 8.0,
      children: [
        MarkdownBody(data: mdQuestion),
        CardRow(
          cards: widget.model.cards,
          height: 60,
        ),
        if (widget.model.reading != null) MarkdownBody(data: widget.model.reading ?? ""),
      ],
    );
  }

  Widget buildJournalBody(String mdQuestion) {
    var surfix = widget.model.reading ?? "";
    if ((widget.model.comment ?? "").isNotEmpty) {
      surfix += "\n\n## ${"Comment".tr}\n\n${widget.model.comment}";
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: 8.0,
      children: [
        MarkdownBody(data: mdQuestion),
        CardRow(
          cards: widget.model.cards,
          height: 60,
        ),
        if (widget.model.reading != null) MarkdownBody(data: surfix),
      ],
    );
  }

  Widget buildDecisionBody(String mdQuestion) {
    return Builder(builder: (context) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 8.0,
        children: [
          MarkdownBody(data: mdQuestion),
          Row(
            children: [
              WaiteTarot.image(
                widget.model.cards[0].cardId,
                widget.model.cards[0].reversed,
                width: 60,
              ),
              const SizedBox(width: 8),
              Text(widget.model.yesno ?? "", style: Theme.of(context).textTheme.titleMedium),
            ],
          ),
          if (widget.model.reading != null) MarkdownBody(data: widget.model.reading ?? ""),
        ],
      );
    });
  }

  Widget buildRatingBody(String mdQuestion) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: 8.0,
      children: [
        MarkdownBody(data: mdQuestion),
        ...List.generate(
          widget.model.cards.length,
          (index) {
            final cardReading = "**${widget.model.cards[index].rating}** "
                ">> ${widget.model.cards[index].title}\n\n"
                "${widget.model.cards[index].reading ?? ""}";
            return Card(
              clipBehavior: Clip.antiAlias,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    WaiteTarot.image(
                      widget.model.cards[index].cardId,
                      widget.model.cards[index].reversed,
                      width: 60,
                    ),
                    SizedBox(width: 8),
                    Expanded(
                      child: MarkdownBody(data: cardReading),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ],
    );
  }
}
