import '../core/llm_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:get/get.dart';

import '../models/channel_enum.dart';
import '../models/tarot_model.dart';
import '../widgets/cards_field/cards_field.dart';

class ChEditScr extends StatefulWidget {
  final GlobalKey<FormState> formKey;
  final TarotModel model;
  const ChEditScr({
    super.key,
    required this.formKey,
    required this.model,
  });

  @override
  State<ChEditScr> createState() => _ChEditScrState();
}

class _ChEditScrState extends State<ChEditScr> {
  final TextEditingController questionController = TextEditingController();
  final TextEditingController optionsController = TextEditingController();
  List<String> enhancedList = [];

  @override
  void initState() {
    questionController.text = widget.model.question;
    optionsController.text = widget.model.options ?? "";
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(8.0),
      child: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 600),
          child: Form(
            key: widget.formKey,
            child: Column(
              spacing: 8.0,
              children: [
                buildQuestion(),
                buildEnhanceQuestion(),
                if (widget.model.channel == ChannelEnum.rating) buildOptions(),
                buildDrewCards(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget buildQuestion() {
    return FormBuilderTextField(
      name: "Question",
      controller: questionController,
      decoration: InputDecoration(
        // border: OutlineInputBorder(),
        labelText: "Question".tr,
        hintText: "Question Hint".tr,
        helperText: (widget.model.channel == ChannelEnum.journal) ? "JournalHelp".tr : null,
        alignLabelWithHint: true,
        filled: true,
      ),
      keyboardType: TextInputType.multiline,
      textInputAction: TextInputAction.newline,
      maxLength: 2000,
      // expands: true,
      autofocus: true,
      minLines: 3,
      maxLines: null,
      validator: (widget.model.channel == ChannelEnum.journal) ? null : FormBuilderValidators.required(),
      onSaved: (newValue) => widget.model.question = newValue ?? "",
    );
  }

  Widget buildEnhanceButton() {
    return IconButton(
      icon: const Icon(Icons.add),
      onPressed: questionController.text.length > 50
          ? null
          : () async {
              Get.dialog(
                const Center(child: CircularProgressIndicator()),
                barrierDismissible: false,
              );
              LlmGroq.I.apikey = groqApiKey;
              LlmGroq.I.modelName = "llama-3.3-70b-specdec";
              final systemPrompt = LlmService.enhancePrompt;
              final userPrompt = questionController.text;
              var resp = await LlmGroq.I.futureText(userPrompt: userPrompt, systemPrompt: systemPrompt) ?? "";
              Get.back();
              // 去除deepseek的</think>之前的內容
              if (resp.contains("</think>")) resp = resp.split("</think>")[1];
              setState(() {
                enhancedList = resp.split("\n").where((e) => e.isNotEmpty).toList();
                // 將List中每一個字串取出第一個空白之後的文字
                enhancedList = enhancedList.map((e) => e.split(" ").skip(1).join(" ")).toList();
              });
            },
    );
  }

  Widget buildEnhanceQuestion() {
    if (enhancedList.isEmpty) return SizedBox.shrink();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: List.generate(
          enhancedList.length,
          (index) => TextButton(
                onPressed: () {
                  questionController.text += enhancedList[index];
                  setState(() {
                    enhancedList = [];
                  });
                },
                child: Text(enhancedList[index]),
              )),
    );
  }

  Widget buildOptions() {
    return FormBuilderTextField(
      name: "Options",
      controller: optionsController,
      decoration: InputDecoration(
        labelText: "Options".tr,
        hintText: "Options Hint".tr,
        filled: true,
      ),
      keyboardType: TextInputType.multiline,
      textInputAction: TextInputAction.newline,
      maxLength: 2000,
      // expands: true,
      autofocus: true,
      minLines: 3,
      maxLines: 9,
      validator: FormBuilderValidators.required(),
      onSaved: (newValue) => widget.model.options = newValue,
    );
  }

  Widget buildDrewCards() {
    return CardsField(
      channel: widget.model.channel,
      questionController: questionController,
      optionsController: optionsController,
      numCards: getNumCards(),
      decoration: InputDecoration(
        labelText: "DrewCards".tr,
        helperText: "點擊手指圖示選牌".tr,
      ),
      onSaved: (drewInfo) {
        if (drewInfo != null) {
          widget.model.drewMode = drewInfo.drewMode;
          widget.model.cards = drewInfo.cards;
          if (widget.model.channel == ChannelEnum.rating) {
            final cardPrompts =
                optionsController.text.split('\n').map((e) => e.trim()).where((element) => element.isNotEmpty).toList();
            for (var i = 0; i < cardPrompts.length; i++) {
              widget.model.cards[i].title = cardPrompts[i];
            }
          }
        }
      },
    );
  }

  int getNumCards() {
    int numCards = 0;
    switch (widget.model.channel) {
      case ChannelEnum.divine:
        numCards = 5;
        break;
      case ChannelEnum.journal:
        numCards = 3;
        break;
      case ChannelEnum.rating:
        var cardPrompts =
            optionsController.text.split('\n').map((e) => e.trim()).where((element) => element.isNotEmpty).toList();
        numCards = cardPrompts.length;
        break;
      case ChannelEnum.decision:
        numCards = 1;
        break;
    }
    return numCards;
  }
}
