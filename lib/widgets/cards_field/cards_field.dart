import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wu_core/widgets/card_drewer.dart';

import '../../constants/waite_tarot.dart';
import '../../models/card_drew.dart';
import '../../models/channel_enum.dart';
import '../core_widgets.dart';

part 'card_row.dart';
part 'drew_info.dart';
part 'drew_page.dart';
part 'drew_page_random.dart';
part 'drew_page_choice.dart';
part 'drew_page_flipcard.dart';

class CardsField extends FormField<DrewInfo?> {
  CardsField({
    super.key,
    required ChannelEnum channel,
    super.initialValue,
    bool? enabled,
    int numCards = 5,
    required TextEditingController questionController,
    TextEditingController? optionsController,
    Function(DrewInfo? cards)? onChanged,
    Function(DrewInfo? cards)? super.onSaved,
    InputDecoration decoration = const InputDecoration(),
  }) : super(
            enabled: enabled ?? decoration.enabled,
            validator: (value) {
              if ((value?.cards.length ?? 0) < numCards) {
                return "Please choose $numCards cards.";
              }
              return null;
            },
            builder: (FormFieldState<DrewInfo?> field) {
              final cards = field.value?.cards ?? [];
              var cardPrompts = <String>[];
              if ((channel == ChannelEnum.rating) &&
                  (optionsController != null) &&
                  (optionsController.text.isNotEmpty)) {
                cardPrompts = optionsController.text
                    .split('\n')
                    .map((e) => e.trim())
                    .where((element) => element.isNotEmpty)
                    .take(9)
                    .toList();
                numCards = cardPrompts.length;
              }
              final colorScheme = Theme.of(field.context).colorScheme;
              return InputDecorator(
                decoration: decoration.copyWith(
                  // border: const OutlineInputBorder(),
                  prefixIcon: field.value?.drewMode.icon,
                  suffixIcon: IconButton(
                    onPressed: channel != ChannelEnum.journal && questionController.text.isEmpty
                        ? null
                        : () async {
                            final drewInfo = await Get.to(
                              () => DrewPage(
                                channel: channel,
                                question: questionController.text,
                                numCards: numCards,
                                cardPrompts: cardPrompts,
                              ),
                            );
                            if (drewInfo != null) field.didChange(drewInfo);
                          },
                    icon: const Icon(Icons.swipe_up),
                    color: colorScheme.primary,
                  ),
                ),
                child: CardRow(
                  cards: cards,
                  height: 60,
                ),
              );
            });
}
