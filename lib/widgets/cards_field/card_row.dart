part of 'cards_field.dart';

class CardRow extends StatelessWidget {
  final double? height;
  final double? width;
  final int? numCards;
  final List<CardDrew> cards;
  final Function(int index)? onCardTap;
  final bool withRating;
  const CardRow({
    super.key,
    required this.cards,
    this.width,
    this.height,
    this.onCardTap,
    this.numCards,
    this.withRating = false,
  });

  @override
  Widget build(BuildContext context) {
    Widget obj = Wrap(
      spacing: 4,
      children: List.generate(cards.length, (index) {
        var ret = WaiteTarot.image(
          cards[index].cardId,
          cards[index].reversed,
          width: width,
          height: height,
        );
        if (withRating) {
          ret = Column(
            children: [
              ret,
              Text(
                cards[index].rating.toString(),
                style: const TextStyle(fontSize: 12, color: Colors.black),
              ),
            ],
          );
        }
        if (onCardTap != null) {
          ret = InkWell(onTap: () => onCardTap?.call(index), child: ret);
        }
        return ret;
      }),
    );
    if (height != null) {
      obj = SizedBox(
        height: height,
        child: obj,
      );
    }
    return obj;
  }
}
