part of 'cards_field.dart';

class DrewPageFlipcard extends StatefulWidget {
  final ChannelEnum channel;
  final int numCards;
  final String question;
  final List<String> cardPrompts;
  final OnCardsDrewChanged? onCardsDrewChanged;
  const DrewPageFlipcard(
      {super.key,
      required this.channel,
      required this.numCards,
      required this.question,
      required this.cardPrompts,
      this.onCardsDrewChanged});

  @override
  State<DrewPageFlipcard> createState() => _DrewPageFlipcardState();
}

class _DrewPageFlipcardState extends State<DrewPageFlipcard> {
  var poolCards = WaiteTarot.cards.keys.map((e) => CardDrew(cardId: e, reversed: false)).toList();
  var drewCards = <CardDrew>[];
  late Random rnd;

  void shuffleCards() {
    rnd = Random(DateTime.now().microsecondsSinceEpoch);
    poolCards.shuffle(rnd);
    final revCount = rnd.nextInt(poolCards.length);
    for (var i = 0; i < revCount; i++) {
      final idx = rnd.nextInt(poolCards.length);
      poolCards[idx].reversed = !poolCards[idx].reversed;
    }
    drewCards = List.generate(widget.numCards, (index) => CardDrew(cardId: "", reversed: false));
  }

  @override
  void initState() {
    shuffleCards();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 8.0,
        children: [
          buildQuestionTile(widget.channel, widget.question),
          (widget.channel == ChannelEnum.rating) ? buildRatingCards() : buildDefaultCards(),
        ],
      ),
    );
  }

  Widget buildDefaultCards() {
    return LayoutBuilder(builder: (context, con) {
      final spacing = 4.0;
      final cardWidth =
          widget.numCards == 1 ? 100.0 : ((con.maxWidth - spacing * (widget.numCards - 1)) / widget.numCards);
      return Center(
        child: Wrap(
          alignment: WrapAlignment.center,
          spacing: spacing,
          children: List.generate(widget.numCards, (index) => buildCard(index, cardWidth)),
        ),
      );
    });
  }

  Widget buildRatingCards() {
    return Expanded(
      child: ListView.builder(
        itemCount: widget.numCards,
        itemBuilder: (context, index) {
          return Row(spacing: 8.0, children: [
            buildCard(index, 60),
            Text(widget.cardPrompts[index]),
          ]);
        },
      ),
    );
  }

  Widget buildCard(int drewIndex, double cardWidth) {
    final card = drewCards[drewIndex];
    final cardBack = WaiteTarot.image("back", false, width: cardWidth);
    final cardFront = card.cardId.isEmpty ? cardBack : WaiteTarot.image(card.cardId, card.reversed, width: cardWidth);

    return FlipCard(
      speed: 500,
      direction: FlipDirection.HORIZONTAL,
      side: drewCards[drewIndex].cardId.isEmpty ? CardSide.BACK : CardSide.FRONT,
      front: cardFront,
      back: cardBack,
      onFlip: () {
        // if (drewCards.where((e) => e.cardId.isNotEmpty).isEmpty) shuffleCards();
        var poolIndex = rnd.nextInt(poolCards.length - 1);
        while (drewCards.where((e) => e.cardId == poolCards[poolIndex].cardId).isNotEmpty) {
          poolIndex = rnd.nextInt(poolCards.length - 1);
        }
        card.cardId = poolCards[poolIndex].cardId;
        card.reversed = poolCards[poolIndex].reversed;
        setState(() {});
      },
      onFlipDone: (isFront) {
        widget.onCardsDrewChanged?.call(drewCards);
      },
      flipOnTouch: drewCards[drewIndex].cardId.isEmpty,
    );
  }
}
