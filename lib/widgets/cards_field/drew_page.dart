part of 'cards_field.dart';

typedef OnCardsDrewChanged = void Function(List<CardDrew> cards);

class DrewPage extends StatefulWidget {
  final ChannelEnum channel;
  final String question;
  final int numCards;
  final List<String> cardPrompts;
  const DrewPage({
    super.key,
    required this.channel,
    required this.question,
    required this.numCards,
    required this.cardPrompts,
  });

  @override
  State<DrewPage> createState() => _CardsDrewPageState();
}

class _CardsDrewPageState extends State<DrewPage> {
  final tabs = [
    Tab(text: "FlipCard".tr),
    Tab(text: "Random".tr),
    Tab(text: "Choice".tr),
  ];
  var drewMode = DrewModeEnum.flipcard;
  var drewCards = <CardDrew>[];

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: DefaultTabController(
        length: tabs.length,
        child: Scaffold(
          appBar: AppBar(
            title: Text("抽牌"),
            actions: [
              IconButton(
                icon: const Icon(Icons.check),
                onPressed: widget.numCards == drewCards.where((e) => e.cardId.isNotEmpty).length
                    ? () => Navigator.pop(context, (DrewInfo(drewMode, drewCards)))
                    : null,
              )
            ],
            bottom: TabBar(
              tabs: tabs,
              isScrollable: true,
              labelStyle: TextStyle(fontWeight: FontWeight.bold),
              onTap: (index) => drewMode = DrewModeEnum.values[index],
            ),
          ),
          body: TabBarView(
            children: List.generate(
              DrewModeEnum.values.length,
              (index) => buildDrewCards(DrewModeEnum.values[index]),
            ),
          ),
        ),
      ),
    );
  }

  Widget buildDrewCards(DrewModeEnum drewMode) {
    switch (drewMode) {
      case DrewModeEnum.flipcard:
        return DrewPageFlipcard(
          channel: widget.channel,
          numCards: widget.numCards,
          question: widget.question,
          cardPrompts: widget.cardPrompts,
          onCardsDrewChanged: (cards) => setState(() => drewCards = cards),
        );
      case DrewModeEnum.random:
        return DrewPageRandom(
          channel: widget.channel,
          numCards: widget.numCards,
          question: widget.question,
          cardPrompts: widget.cardPrompts,
          onCardsDrewChanged: (cards) => setState(() => drewCards = cards),
        );
      case DrewModeEnum.choice:
        return DrewPageChoice(
          channel: widget.channel,
          numCards: widget.numCards,
          question: widget.question,
          cardPrompts: widget.cardPrompts,
          onCardsDrewChanged: (cards) => setState(() => drewCards = cards),
        );
    }
  }
}

Widget buildQuestionTile(ChannelEnum channel, String question) {
  var helpText = ((channel == ChannelEnum.journal) && (question.isEmpty)) ? "請默念：今天的運勢如何".tr : "請默念要占卜的問題".tr;
  return Builder(builder: (context) {
    final textTheme = Theme.of(context).textTheme;
    return Column(crossAxisAlignment: CrossAxisAlignment.start, spacing: 8.0, children: [
      if (question.isNotEmpty) Text(question, style: textTheme.titleMedium),
      Text(helpText, style: textTheme.bodyMedium),
    ]);
  });
}
