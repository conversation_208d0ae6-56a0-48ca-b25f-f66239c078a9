part of 'cards_field.dart';

class DrewPageRandom extends StatefulWidget {
  final ChannelEnum channel;
  final int numCards;
  final String question;
  final List<String> cardPrompts;
  final OnCardsDrewChanged? onCardsDrewChanged;
  const DrewPageRandom({
    super.key,
    required this.channel,
    required this.numCards,
    required this.question,
    required this.cardPrompts,
    this.onCardsDrewChanged,
  });

  @override
  State<DrewPageRandom> createState() => _DrewPageRandomState();
}

class _DrewPageRandomState extends State<DrewPageRandom> {
  var poolCards = WaiteTarot.cards.keys.map((e) => CardDrew(cardId: e, reversed: false)).toList();
  var drewCards = <CardDrew>[];

  @override
  void initState() {
    shuffleCards();
    super.initState();
  }

  void shuffleCards() {
    final rnd = Random(DateTime.now().microsecondsSinceEpoch);
    poolCards.shuffle(rnd);
    final revCount = rnd.nextInt(poolCards.length);
    for (var i = 0; i < revCount; i++) {
      final idx = rnd.nextInt(poolCards.length);
      poolCards[idx].reversed = !poolCards[idx].reversed;
    }
    drewCards.clear();
  }

  @override
  Widget build(BuildContext context) {
    return DeviceBuilder(builder: (context, con, deviceType, platform) {
      final cardWidth = switch (deviceType) {
        DeviceType.mobile => 60.0,
        DeviceType.tablet => 70.0,
        DeviceType.desktop => 80.0,
        DeviceType.largeScreen => 100.0,
      };
      final crossAxisCount = MediaQuery.of(context).size.width ~/ cardWidth;
      final colorTheme = Theme.of(context).colorScheme;
      final textTheme = Theme.of(context).textTheme;
      final optionStyle = textTheme.headlineLarge?.copyWith(color: colorTheme.primary);
      final sernoStyle = TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.bold,
        // color: Colors.white,
        foreground: Paint()
          ..style = PaintingStyle.stroke
          ..strokeWidth = 3
          ..color = Colors.purple.shade700,
      );
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 8.0,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                buildQuestionTile(widget.channel, widget.question),
                if ((widget.channel == ChannelEnum.rating) && (drewCards.length < widget.numCards))
                  Text(
                    ">> ${widget.cardPrompts[drewCards.length]}",
                    style: optionStyle,
                  ),
                InputDecorator(
                  decoration: InputDecoration(
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(8.0)),
                    counterText: "${drewCards.length} / ${widget.numCards}",
                  ),
                  child: CardRow(cards: drewCards, numCards: widget.numCards, height: 60),
                ),
              ],
            ),
          ),
          Expanded(
            child: SingleChildScrollView(
              child: CardDrewer(
                spacing: 2,
                cardCount: poolCards.length,
                crossAxisCount: crossAxisCount,
                frontBuilder: (index, cardWidth) =>
                    WaiteTarot.image(poolCards[index].cardId, poolCards[index].reversed, width: cardWidth),
                backBuilder: (index, cardWidth) {
                  return Stack(alignment: Alignment.center, children: [
                    WaiteTarot.image("back", false, width: cardWidth),
                    // 標註卡牌序號，提供遠端選牌時指定
                    Text("${index + 1}", style: sernoStyle),
                    Text("${index + 1}", style: TextStyle(color: Colors.white, fontSize: 24)),
                  ]);
                },
                cardSide: (index) {
                  // 開始時卡背朝上
                  return drewCards.where((e) => e.cardId == poolCards[index].cardId).isEmpty
                      ? CardSide.BACK
                      : CardSide.FRONT;
                },
                enabledFlip: (index) {
                  return drewCards.length < widget.numCards &&
                      drewCards.where((e) => e.cardId == poolCards[index].cardId).isEmpty;
                },
                onFlipDone: (index) {
                  // if (drewCards.isEmpty) shuffleCards();
                  drewCards.add(poolCards[index]);
                  widget.onCardsDrewChanged?.call(drewCards);
                  setState(() {});
                },
              ),
            ),
          ),
        ],
      );
    });
  }
}
