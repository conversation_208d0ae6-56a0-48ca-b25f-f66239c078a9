part of 'cards_field.dart';

class DrewPageChoice extends StatefulWidget {
  final ChannelEnum channel;
  final int numCards;
  final String question;
  final List<String> cardPrompts;
  final OnCardsDrewChanged? onCardsDrewChanged;
  const DrewPageChoice({
    super.key,
    required this.channel,
    required this.numCards,
    required this.question,
    required this.cardPrompts,
    this.onCardsDrewChanged,
  });

  @override
  State<DrewPageChoice> createState() => _DrewPageChoiceState();
}

class _DrewPageChoiceState extends State<DrewPageChoice> {
  var poolCards = WaiteTarot.cards.keys.map((e) => CardDrew(cardId: e, reversed: false)).toList();
  var drewCards = <CardDrew>[];
  var category = "M";

  @override
  Widget build(BuildContext context) {
    return DeviceBuilder(builder: (context, con, deviceType, platform) {
      final cardWidth = switch (deviceType) {
        DeviceType.mobile => 70.0,
        DeviceType.tablet => 80.0,
        DeviceType.desktop => 90.0,
        DeviceType.largeScreen => 120.0,
      };

      final crossAxisCount = MediaQuery.of(context).size.width ~/ cardWidth;
      final colorTheme = Theme.of(context).colorScheme;
      final textTheme = Theme.of(context).textTheme;
      final optionStyle = textTheme.headlineLarge?.copyWith(color: colorTheme.primary);

      final listCards = poolCards.where((e) => e.cardId.startsWith(category)).toList();
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 8.0,
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: 8.0,
              children: [
                if (widget.channel != ChannelEnum.journal) Text(widget.question),
                if ((widget.channel == ChannelEnum.rating) && (drewCards.length < widget.numCards))
                  Text(
                    ">> ${widget.cardPrompts[drewCards.length]}",
                    style: optionStyle,
                  ),
                InputDecorator(
                  decoration: InputDecoration(
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(8.0)),
                    counterText: "${drewCards.length} / ${widget.numCards}",
                  ),
                  child: CardRow(
                    cards: drewCards,
                    numCards: widget.numCards,
                    height: 60,
                    onCardTap: (index) => setState(() => drewCards[index].reversed = !drewCards[index].reversed),
                  ),
                ),
              ],
            ),
          ),
          buildCategoryRow(),
          Expanded(
            child: SingleChildScrollView(
              child: CardDrewer(
                redraw: true,
                spacing: 2,
                cardCount: listCards.length,
                crossAxisCount: crossAxisCount,
                frontBuilder: (index, cardWidth) => WaiteTarot.image(
                  listCards[index].cardId,
                  listCards[index].reversed,
                  width: cardWidth,
                ),
                backBuilder: (index, cardWidth) => WaiteTarot.image(
                  "back",
                  false,
                  width: cardWidth,
                ),
                cardSide: (index) {
                  // 開始時卡背朝下
                  return drewCards.where((e) => e.cardId == listCards[index].cardId).isEmpty
                      ? CardSide.FRONT
                      : CardSide.BACK;
                },
                enabledFlip: (index) {
                  return drewCards.length < widget.numCards &&
                      drewCards.where((e) => e.cardId == listCards[index].cardId).isEmpty;
                },
                onFlipDone: (index) {
                  drewCards.add(listCards[index]);
                  widget.onCardsDrewChanged?.call(drewCards);
                  setState(() {});
                },
              ),
            ),
          ),
        ],
      );
    });
  }

  Widget buildCategoryRow() {
    final items = {
      "M": WaiteTarot.imageMajor(),
      "S": WaiteTarot.imageSword(),
      "C": WaiteTarot.imageCup(),
      "P": WaiteTarot.imagePentacle(),
      "W": WaiteTarot.imageWand(),
    };
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.secondaryContainer,
      ),
      child: Row(
        children: items.keys
            .map((k) => IconButton(
                  onPressed: () => setState(() => category = k),
                  icon: items[k],
                ))
            .toList(),
      ),
    );
  }
}
