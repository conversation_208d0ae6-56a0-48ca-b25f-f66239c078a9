import 'package:flutter/material.dart';

// 設備類型枚舉
enum DeviceType {
  mobile,
  tablet,
  desktop,
  largeScreen,
}

// 平台類型枚舉
enum PlatformType {
  android,
  ios,
  web,
  windows,
  macos,
  linux,
}

// DeviceBuilder 元件
typedef DeviceContentBuilder = Widget Function(
  BuildContext context,
  BoxConstraints constraints,
  DeviceType deviceType,
  PlatformType platform,
);

class DeviceBuilder extends StatelessWidget {
  final DeviceContentBuilder builder;

  const DeviceBuilder({
    super.key,
    required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;

        // 判斷設備類型
        final deviceType = width < 600
            ? DeviceType.mobile
            : width < 900
                ? DeviceType.tablet
                : width < 1200
                    ? DeviceType.desktop
                    : DeviceType.largeScreen;

        // 判斷平台類型
        final platform = Theme.of(context).platform == TargetPlatform.android
            ? PlatformType.android
            : Theme.of(context).platform == TargetPlatform.iOS
                ? PlatformType.ios
                : Theme.of(context).platform == TargetPlatform.windows
                    ? PlatformType.windows
                    : Theme.of(context).platform == TargetPlatform.macOS
                        ? PlatformType.macos
                        : Theme.of(context).platform == TargetPlatform.linux
                            ? PlatformType.linux
                            : PlatformType.web;

        return builder(context, constraints, deviceType, platform);
      },
    );
  }
}

// DateHeader 元件
class DateHeader extends StatelessWidget {
  final DateTime date;
  final String? title;

  const DateHeader({
    super.key,
    required this.date,
    this.title,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Icon(
            Icons.calendar_today,
            size: 16,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Text(
            title ?? "${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}",
            style: theme.textTheme.titleSmall?.copyWith(
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}

// ChannelTile 元件
class ChannelTile extends StatelessWidget {
  final Color color;
  final Widget leading;
  final Widget title;
  final Widget subtitle;
  final VoidCallback? onTap;

  const ChannelTile({
    super.key,
    required this.color,
    required this.leading,
    required this.title,
    required this.subtitle,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: color.withValues(alpha: 0.3)),
          ),
          child: Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: leading,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    DefaultTextStyle(
                      style: theme.textTheme.titleMedium!,
                      child: title,
                    ),
                    const SizedBox(height: 4),
                    DefaultTextStyle(
                      style: theme.textTheme.bodySmall!.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      child: subtitle,
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
