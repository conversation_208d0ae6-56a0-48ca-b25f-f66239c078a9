import 'package:flex_color_scheme/flex_color_scheme.dart';
import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:upgrader/upgrader.dart';
import 'package:window_manager/window_manager.dart';
import 'package:wu_admob/wu_admob.dart';

import 'pages/home_page.dart';
import 'providers/setting_controller.dart';
import 'providers/setting_provider.dart';
import 'services/hive_service.dart';
import 'services/langpack_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await GetStorage.init();

  // 桌面版取回離開時的視窗大小
  if (Platform.isWindows || Platform.isMacOS || Platform.isLinux) {
    final storage = GetStorage();
    final width = storage.read<double>('window_width') ?? 800.0;
    final height = storage.read<double>('window_height') ?? 600.0;
    // 只有桌面環境才初始化 `window_manager`
    await windowManager.ensureInitialized();
    await windowManager.setSize(Size(width, height));
  }

  await HiveService.I.init();

  // 初始化語言包
  Get.put(SettingProvider());
  // init reward controller
  Get.put(RewardController(
    androidAdId: "ca-app-pub-4369686218181569/**********",
    iosAdId: "ca-app-pub-4369686218181569/**********",
    initTickCount: 10,
    watchDuration: Duration(seconds: 30),
  ));

  // init store review controller
  StoreReview.I.init(minRemindDays: 7, minUseCoins: 5);

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    const scheme = FlexScheme.purpleBrown;
    final appBarStyle = FlexAppBarStyle.primary;
    final tabBarStyle = FlexTabBarStyle.universal;

    return GetMaterialApp(
      title: "AI TAROT MASTER",
      debugShowCheckedModeBanner: false,
      translations: LangPackService(),
      locale: Get.deviceLocale,
      fallbackLocale: const Locale('en', 'US'),
      themeMode: ThemeMode.system,
      theme: FlexThemeData.light(
        scheme: scheme,
        appBarStyle: appBarStyle,
        tabBarStyle: tabBarStyle,
        // useMaterial3: true,
      ),
      darkTheme: FlexThemeData.dark(
        scheme: scheme,
        appBarStyle: appBarStyle,
        tabBarStyle: tabBarStyle,
        // useMaterial3: true,
      ),
      home: UpgradeAlert(
        child: const HomePage(),
      ),
    );
  }
}
