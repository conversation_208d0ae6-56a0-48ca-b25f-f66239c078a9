import 'package:flutter/services.dart';
import 'package:get_storage/get_storage.dart';

import '../constants/languages.dart';
import 'setting_controller.dart';

/// 設定提供者，擴展 SettingController 並添加額外功能
class SettingProvider extends SettingController {
  LanguageEnum readingLanguage = LanguageEnum.zh;

  SettingProvider() {
    loadSettings();
  }

  /// 頻道資訊 (標題, 描述)
  List<(String, String)> get channels => [
        (
          "Divination".tr,
          "Want to know the path ahead? The Divination mode uses five tarot cards to identify the key card, unveiling the mysteries of fate."
              .tr
        ),
        (
          "Journal".tr,
          "The three-card daily journal tarot reading is a simple yet insightful method for gaining daily guidance.".tr
        ),
        (
          "Rating".tr,
          "Struggling to decide between multiple options? The Rating mode draws a tarot card for each choice.".tr
        ),
        (
          "Decision".tr,
          "Need a clear answer? The Decision mode lets you draw one tarot card for direct 'Yes' or 'No' guidance.".tr
        ),
      ];

  @override
  void loadSettings() {
    super.loadSettings();
    final storage = GetStorage();
    final langName = storage.read("readingLanguage");
    readingLanguage = LanguageEnum.parse(langName);
  }

  Future _saveSettings() async {
    await save();
    final storage = GetStorage();
    await storage.write("readingLanguage", readingLanguage.name);
  }

  /// 儲存設定
  Future<void> save() async {
    await _saveSettings();
  }

  /// 載入文檔
  Future<String> loadDoc(String docPath) async {
    try {
      final fullPath = "assets/docs/${readingLanguage.name}/$docPath";
      return await rootBundle.loadString(fullPath);
    } catch (e) {
      // 如果載入失敗，嘗試載入英文版本
      try {
        final fallbackPath = "assets/docs/en/$docPath";
        return await rootBundle.loadString(fallbackPath);
      } catch (e) {
        return "Document not found: $docPath";
      }
    }
  }
}
