import 'dart:convert';
import 'dart:io';

import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:path_provider/path_provider.dart';
import 'package:wu_llm/wu_llm.dart';
export 'package:get/get.dart';

/// 設定服務
class SettingController extends GetxController {
  String? _serviceName;
  Map<String, String?> apiKeys = {};
  Map<String, String?> modelNames = {};
  String? prefixPrompt;

  SettingController() {
    loadSettings();
  }

  @override
  void update([List<Object>? ids, bool condition = true]) {
    _saveSettings();
    super.update(ids, condition);
  }

  final serviceList = ["Gemini", "Groq", "ChatGPT"];
  String get serviceName => _serviceName ?? serviceList.first;
  set serviceName(String value) => _serviceName = value;

  String? get apiKey => apiKeys[serviceName];
  set apiKey(String? value) => apiKeys[serviceName] = value;

  String? get modelName => modelNames[serviceName];
  set modelName(String? value) => modelNames[serviceName] = value;

  LlmBase get llmService => LlmBase.getService(serviceName);

  Future<List<String>> getModels() async {
    try {
      return llmService.getModels(apiKey!);
    } catch (e) {
      return [];
    }
  }

  /// 讀取設定
  void loadSettings() {
    final storage = GetStorage();
    _serviceName = storage.read("serviceName");
    prefixPrompt = storage.read("prefixPrompt");
    for (var s in serviceList) {
      apiKeys[s] = storage.read("${s}_apiKey");
      modelNames[s] = storage.read("${s}_modelName");
    }
    // print("serviceName: $serviceName, apiKey: $apiKey, modelName: $modelName");
  }

  /// 儲存設定
  Future _saveSettings() async {
    final storage = GetStorage();
    await storage.write("serviceName", serviceName);
    await storage.write("prefixPrompt", prefixPrompt);
    for (var s in serviceList) {
      await storage.write("${s}_apiKey", apiKeys[s]);
      await storage.write("${s}_modelName", modelNames[s]);
    }
  }

  Future reset() async {
    final storage = GetStorage();
    await storage.erase();
    loadSettings();
    update();
  }

  /// 備份檔案名稱
  final backupFileName = "adv_yijing_backup.json";

  /// 以 json 格式備份設定
  Future jsonBackup() async {
    final tempDir = await getTemporaryDirectory();
    final backupFile = File("${tempDir.path}/$backupFileName");
    final setting = {
      "serviceName": serviceName,
      "prefixPrompt": prefixPrompt,
    };
    for (var s in serviceList) {
      setting["${s}_apiKey"] = apiKeys[s];
      setting["${s}_modelName"] = modelNames[s];
    }
    final json = jsonEncode(setting);
    await backupFile.writeAsString(json);
  }

  /// 以 json 格式還原設定
  Future jsonRestore() async {
    final tempDir = await getTemporaryDirectory();
    final backupFile = File("${tempDir.path}/$backupFileName");
    if (!backupFile.existsSync()) throw "備份檔案不存在";
    final json = await backupFile.readAsString();
    final setting = jsonDecode(json);
    serviceName = setting["serviceName"];
    prefixPrompt = setting["prefixPrompt"];
    for (var s in serviceList) {
      apiKeys[s] = setting["${s}_apiKey"];
      modelNames[s] = setting["${s}_modelName"];
    }
    update();
  }
}
