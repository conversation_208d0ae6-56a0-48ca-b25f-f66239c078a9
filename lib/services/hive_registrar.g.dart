// Generated by Hive CE
// Do not modify
// Check in to version control

import 'package:hive_ce/hive.dart';
import 'package:adv_tarot/services/hive_adapters.dart';

extension HiveRegistrar on HiveInterface {
  void registerAdapters() {
    registerAdapter(CardDrewAdapter());
    registerAdapter(ChannelEnumAdapter());
    registerAdapter(DrewModeEnumAdapter());
    registerAdapter(TarotModelAdapter());
  }
}
