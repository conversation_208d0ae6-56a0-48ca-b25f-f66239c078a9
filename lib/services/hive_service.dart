import 'dart:io';

import 'package:hive_ce_flutter/hive_flutter.dart';
import 'package:path_provider/path_provider.dart';

import 'package:adv_tarot/services/hive_registrar.g.dart';

import '../models/tarot_model.dart';

class HiveService {
  static HiveService I = HiveService();
  HiveService();

  final boxName = "tarot";
  late Box<TarotModel> box;

  Future<void> init() async {
    final Directory appDocumentsDir = await getApplicationDocumentsDirectory();
    Hive
      ..init(appDocumentsDir.path)
      ..registerAdapters();
    // await Hive.deleteBoxFromDisk(boxName); // delete old box();
    box = await Hive.openBox<TarotModel>(boxName);
  }

  Future<void> clear() async {
    await Hive.close();
    await Hive.deleteBoxFromDisk(boxName);
    await init();
  }
}
