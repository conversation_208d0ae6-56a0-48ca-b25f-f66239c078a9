// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hive_adapters.dart';

// **************************************************************************
// AdaptersGenerator
// **************************************************************************

class TarotModelAdapter extends TypeAdapter<TarotModel> {
  @override
  final int typeId = 0;

  @override
  TarotModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return TarotModel()
      ..createAt = fields[0] as DateTime
      ..updateAt = fields[1] as DateTime
      ..question = fields[3] as String
      ..comment = fields[4] as String?
      ..yesno = fields[5] as String?
      ..channel = fields[7] as ChannelEnum
      ..drewMode = fields[8] as DrewModeEnum
      ..reading = fields[9] as String?
      ..cards = (fields[10] as List).cast<CardDrew>()
      ..options = fields[11] as String?
      ..summary = fields[12] as String?;
  }

  @override
  void write(BinaryWriter writer, TarotModel obj) {
    writer
      ..writeByte(11)
      ..writeByte(0)
      ..write(obj.createAt)
      ..writeByte(1)
      ..write(obj.updateAt)
      ..writeByte(3)
      ..write(obj.question)
      ..writeByte(4)
      ..write(obj.comment)
      ..writeByte(5)
      ..write(obj.yesno)
      ..writeByte(7)
      ..write(obj.channel)
      ..writeByte(8)
      ..write(obj.drewMode)
      ..writeByte(9)
      ..write(obj.reading)
      ..writeByte(10)
      ..write(obj.cards)
      ..writeByte(11)
      ..write(obj.options)
      ..writeByte(12)
      ..write(obj.summary);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TarotModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ChannelEnumAdapter extends TypeAdapter<ChannelEnum> {
  @override
  final int typeId = 3;

  @override
  ChannelEnum read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return ChannelEnum.divine;
      case 2:
        return ChannelEnum.rating;
      case 3:
        return ChannelEnum.decision;
      case 6:
        return ChannelEnum.journal;
      default:
        return ChannelEnum.divine;
    }
  }

  @override
  void write(BinaryWriter writer, ChannelEnum obj) {
    switch (obj) {
      case ChannelEnum.divine:
        writer.writeByte(0);
      case ChannelEnum.rating:
        writer.writeByte(2);
      case ChannelEnum.decision:
        writer.writeByte(3);
      case ChannelEnum.journal:
        writer.writeByte(6);
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ChannelEnumAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class DrewModeEnumAdapter extends TypeAdapter<DrewModeEnum> {
  @override
  final int typeId = 4;

  @override
  DrewModeEnum read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 1:
        return DrewModeEnum.choice;
      case 3:
        return DrewModeEnum.flipcard;
      case 4:
        return DrewModeEnum.random;
      default:
        return DrewModeEnum.choice;
    }
  }

  @override
  void write(BinaryWriter writer, DrewModeEnum obj) {
    switch (obj) {
      case DrewModeEnum.choice:
        writer.writeByte(1);
      case DrewModeEnum.flipcard:
        writer.writeByte(3);
      case DrewModeEnum.random:
        writer.writeByte(4);
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DrewModeEnumAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CardDrewAdapter extends TypeAdapter<CardDrew> {
  @override
  final int typeId = 5;

  @override
  CardDrew read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return CardDrew(
      cardId: fields[0] as String,
      reversed: fields[1] as bool,
    )
      ..title = fields[2] as String?
      ..rating = (fields[3] as num).toInt()
      ..reading = fields[6] as String?;
  }

  @override
  void write(BinaryWriter writer, CardDrew obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.cardId)
      ..writeByte(1)
      ..write(obj.reversed)
      ..writeByte(2)
      ..write(obj.title)
      ..writeByte(3)
      ..write(obj.rating)
      ..writeByte(6)
      ..write(obj.reading);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CardDrewAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
