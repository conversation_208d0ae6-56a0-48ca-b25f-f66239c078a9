import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wu_admob/wu_admob.dart';

import '../core/llm_service.dart';

import '../models/channel_enum.dart';
import '../models/tarot_model.dart';
import '../providers/setting_provider.dart';
import '../screens/ch_view_scr.dart';

class ChViewPage extends StatefulWidget {
  final TarotModel model;
  const ChViewPage({super.key, required this.model});

  @override
  State<ChViewPage> createState() => _ChViewPageState();
}

class _ChViewPageState extends State<ChViewPage> {
  @override
  Widget build(BuildContext context) {
    final provider = Get.find<SettingProvider>();
    final colorScheme = Theme.of(context).colorScheme;

    // start stroe review
    StoreReview.I.show();

    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          centerTitle: false,
          title: Text(provider.channels[widget.model.channel.index].$1),
          actions: [
            buildReadingButton(),
          ],
        ),
        body: SingleChildScrollView(
          child: Center(
            child: ConstrainedBox(
              constraints: BoxConstraints(maxWidth: 600, minWidth: 300),
              child: Container(
                color: colorScheme.surfaceContainer,
                padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
                child: ChViewScr(model: widget.model),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget buildReadingButton() {
    final useCoin = switch (widget.model.channel) {
      ChannelEnum.decision => 0,
      ChannelEnum.divine => 2,
      ChannelEnum.journal => 1,
      ChannelEnum.rating => 2,
    };
    final colorScheme = Theme.of(context).colorScheme;
    return ReadingButton(
      decCoins: useCoin,
      foreColor: colorScheme.onPrimaryContainer,
      backColor: colorScheme.primaryContainer,
      readingText: "Reading".tr,
      onReadingTap: () async {
        try {
          final (systemPrompt, userPrompt) = LlmService.getPrompt2(widget.model);
          final resp = await LlmChoice.I.futureText(userPrompt: userPrompt, systemPrompt: systemPrompt);
          if (resp != null) {
            LlmService.decode(widget.model, resp);
          }
          widget.model.save();
          // store use coins add
          StoreReview.I.useCoinsAdd();
          setState(() {});
          return true;
        } catch (e) {
          Get.snackbar("Error".tr, e.toString());
        }
        return false;
      },
    );
  }
}
