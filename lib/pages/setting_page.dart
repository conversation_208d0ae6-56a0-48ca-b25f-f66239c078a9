import 'package:flutter/material.dart';
import '../providers/setting_controller.dart';
import '../screens/setting_scr.dart';

class SettingPage extends StatelessWidget {
  const SettingPage({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          title: Text("Settings".tr),
          actions: [
            IconButton(
              icon: const Icon(Icons.cleaning_services_outlined),
              onPressed: () async {
                Get.defaultDialog(
                  title: "Clean.Dialog.title".tr,
                  content: Text("Clean.Dialog.content".tr),
                  confirm: TextButton(
                    child: Text("OK".tr),
                    onPressed: () async {
                      await Get.find<SettingController>().reset();
                      Get.back();
                    },
                  ),
                  cancel: TextButton(
                    child: Text("Cancel".tr),
                    onPressed: () => Get.back(),
                  ),
                );
              },
            )
          ],
        ),
        body: SettingScr(),
      ),
    );
  }
}
