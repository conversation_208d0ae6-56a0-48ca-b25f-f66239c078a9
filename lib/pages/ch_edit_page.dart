import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../models/tarot_model.dart';
import '../providers/setting_provider.dart';
import '../screens/ch_edit_scr.dart';
import 'ch_view_page.dart';

class ChEditPage extends StatelessWidget {
  final TarotModel model;
  ChEditPage({super.key, required this.model});

  final formKey = GlobalKey<FormState>();
  final provider = Get.find<SettingProvider>();

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          title: Text(provider.channels[model.channel.index].$1),
          actions: [
            IconButton(
              icon: const Icon(Icons.check),
              onPressed: () {
                if (formKey.currentState!.validate() == false) return;
                formKey.currentState!.save();
                model.channel = model.channel;
                model.save();
                Get.off(() => ChViewPage(model: model));
              },
            )
          ],
        ),
        body: ChEditScr(formKey: formKey, model: model),
      ),
    );
  }
}
