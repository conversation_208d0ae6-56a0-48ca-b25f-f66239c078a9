import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';

import '../screens/profile_scr.dart';

class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    final formKey = GlobalKey<FormBuilderState>();
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          title: Text("Profile".tr),
        ),
        body: Padding(
          padding: const EdgeInsets.all(8.0),
          child: ProfileScr(formKey: formKey),
        ),
      ),
    );
  }
}
