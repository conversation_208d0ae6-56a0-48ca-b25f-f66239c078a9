import 'package:adv_tarot/models/tarot_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';

class CommentPage extends StatelessWidget {
  final TarotModel model;
  const CommentPage({super.key, required this.model});

  @override
  Widget build(BuildContext context) {
    final formKey = GlobalKey<FormBuilderState>();
    return SafeArea(
      child: FormBuilder(
        key: formKey,
        child: Scaffold(
          appBar: AppBar(
            title: Text("Comment".tr),
            actions: [
              buildSaveButton(formKey),
            ],
          ),
          body: Padding(
            padding: const EdgeInsets.all(8.0),
            child: buildCommentField(),
          ),
        ),
      ),
    );
  }

  Widget buildSaveButton(GlobalKey<FormBuilderState> formKey) {
    return IconButton(
      onPressed: () {
        if (formKey.currentState == null) return;
        formKey.currentState!.save();
        model.save();
        Get.back();
      },
      icon: Icon(Icons.check),
    );
  }

  Widget buildCommentField() {
    return SizedBox(
      height: 300,
      child: FormBuilderTextField(
        name: "Comment",
        initialValue: model.comment,
        decoration: InputDecoration(
          labelText: "Comment".tr,
          hintText: "Comment Hint".tr,
          alignLabelWithHint: true,
          filled: true,
        ),
        keyboardType: TextInputType.multiline,
        textInputAction: TextInputAction.newline,
        maxLength: 2000,
        expands: true,
        autofocus: true,
        maxLines: null,
        onSaved: (newValue) => model.comment = newValue,
      ),
    );
  }
}
