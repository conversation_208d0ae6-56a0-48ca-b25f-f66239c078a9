import 'package:flutter/material.dart';

import '../screens/doc_view_scr.dart';

class DocViewPage extends StatelessWidget {
  final String docPath;
  const DocViewPage({super.key, required this.docPath});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(),
        body: Center(
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 600),
            child: DocViewScr(docPath: docPath),
          ),
        ),
      ),
    );
  }
}
