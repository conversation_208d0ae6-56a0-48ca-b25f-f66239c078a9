import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../models/channel_enum.dart';
import '../models/tarot_model.dart';
import '../providers/setting_provider.dart';
import '../screens/ch_list_scr.dart';
import 'ch_edit_page.dart';

class ChListPage extends StatelessWidget {
  final ChannelEnum channel;
  const ChListPage({super.key, required this.channel});

  @override
  Widget build(BuildContext context) {
    final provider = Get.find<SettingProvider>();
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          // flexibleSpace: Container(
          //   decoration: BoxDecoration(
          //     image: DecorationImage(
          //       image: AssetImage('assets/images/banners/${channel.name}.png'), // 背景圖片
          //       fit: BoxFit.cover, // 讓圖片填滿 AppBar
          //     ),
          //   ),
          // ),
          title: Text(provider.channels[channel.index].$1),
          actions: [
            IconButton(
              icon: Icon(Icons.add),
              onPressed: () {
                Get.to(() => ChEditPage(model: TarotModel()..channel = channel));
              },
            )
          ],
        ),
        body: Center(
          child: ChListScr(channel: channel),
        ),
      ),
    );
  }
}
