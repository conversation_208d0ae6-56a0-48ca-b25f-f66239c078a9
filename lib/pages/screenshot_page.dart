import 'package:flutter/material.dart';
import 'package:screenshot/screenshot.dart';

class ScreenshotPage extends StatelessWidget {
  final ScreenshotController screenshotController;
  final Widget content; // 需要截圖的內容

  const ScreenshotPage({super.key, required this.screenshotController, required this.content});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Center(
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 600),
            child: Screenshot(
              controller: screenshotController,
              child: Theme(
                data: ThemeData.light(), // 強制使用淺色模式
                child: Container(
                  color: Colors.white, // 確保背景是白色
                  padding: const EdgeInsets.all(16.0),
                  child: content,
                ), // 要截圖的 Widget
              ),
            ),
          ),
        ),
      ),
    );
  }
}
