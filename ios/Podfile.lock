PODS:
  - device_info_plus (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - flutter_native_splash (2.4.3):
    - Flutter
  - Google-Mobile-Ads-SDK (11.13.0):
    - GoogleUserMessagingPlatform (>= 1.1)
  - google_mobile_ads (5.3.1):
    - Flutter
    - Google-Mobile-Ads-SDK (~> 11.13.0)
    - webview_flutter_wkwebview
  - GoogleUserMessagingPlatform (2.7.0)
  - in_app_review (2.0.0):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - url_launcher_ios (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - Flutter (from `Flutter`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - google_mobile_ads (from `.symlinks/plugins/google_mobile_ads/ios`)
  - in_app_review (from `.symlinks/plugins/in_app_review/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - Google-Mobile-Ads-SDK
    - GoogleUserMessagingPlatform

EXTERNAL SOURCES:
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  Flutter:
    :path: Flutter
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  google_mobile_ads:
    :path: ".symlinks/plugins/google_mobile_ads/ios"
  in_app_review:
    :path: ".symlinks/plugins/in_app_review/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  device_info_plus: bf2e3232933866d73fe290f2942f2156cdd10342
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_native_splash: f71420956eb811e6d310720fee915f1d42852e7a
  Google-Mobile-Ads-SDK: 14f57f2dc33532a24db288897e26494640810407
  google_mobile_ads: fe0e2c1764ad95323dd0e3081d0bb2d58411f957
  GoogleUserMessagingPlatform: a8b56893477f67212fbc8411c139e61d463349f5
  in_app_review: a31b5257259646ea78e0e35fc914979b0031d011
  package_info_plus: c0502532a26c7662a62a356cebe2692ec5fe4ec4
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  share_plus: 8b6f8b3447e494cca5317c8c3073de39b3600d1f
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe
  webview_flutter_wkwebview: a4af96a051138e28e29f60101d094683b9f82188

PODFILE CHECKSUM: 819463e6a0290f5a72f145ba7cde16e8b6ef0796

COCOAPODS: 1.16.2
