{"buildFiles": ["/Volumes/DATA/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Volumes/DATA/codes/adv_tarot/android/app/.cxx/Debug/2y443c6o/arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Volumes/DATA/codes/adv_tarot/android/app/.cxx/Debug/2y443c6o/arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}