import 'jianchu_dict.dart';

import '../wu_zeri.dart';

class Zeri {
  final GzDate gzdate;

  Zeri(this.gzdate);
  factory Zeri.bySolar(DateTime solar) {
    return Zeri(GzDate.bySolar(solar));
  }

  /// 建除十二星
  late Map<String, String> jianchu = getJianchu();
  Map<String, String> getJianchu() {
    // 黃道吉日：除、危、定、執、成、開（吉）
    // 黑道吉日：建、滿、平、收、閉、破（凶）
    const items = "建除滿平定執破危成收開閉";

    var index = (12 - gzdate.m.zhi.index + gzdate.d.zhi.index) % 12;
    var star12 = items[index];
    return JIANCHU_DICT[star12]!;
  }

  String star28(DateTime solar) {
    // final items = "虛危室壁奎婁胃昴畢觜參井鬼柳星張翼軫角亢氐房心尾箕斗牛女";
    return "";
  }

  /// 喜神方
  /// 喜神方位歌
  /// 甲已在艮乙庚乾，丙辛坤位喜神安；
  /// 丁壬只在離宮坐，戊癸游來在巽間。
  String get god1Direction {
    var result = "喜神";
    if ("甲己".contains(gzdate.d.gan.name)) result += "東北";
    if ("乙庚".contains(gzdate.d.gan.name)) result += "西北";
    if ("丙辛".contains(gzdate.d.gan.name)) result += "西南";
    if ("丁壬".contains(gzdate.d.gan.name)) result += "南方";
    if ("戊癸".contains(gzdate.d.gan.name)) result += "東南";
    return result;
  }

  /// 福神方位歌
  /// 甲乙東南是福神，丙丁正東是堪宜；
  /// 戊北己南庚辛坤，壬在乾方癸在酉。
  String get god2Direction {
    var result = "福神";
    if ("甲乙".contains(gzdate.d.gan.name)) result += "東南";
    if ("丙丁".contains(gzdate.d.gan.name)) result += "東方";
    if ("戊".contains(gzdate.d.gan.name)) result += "北方";
    if ("己".contains(gzdate.d.gan.name)) result += "南方";
    if ("庚辛".contains(gzdate.d.gan.name)) result += "西南";
    if ("壬".contains(gzdate.d.gan.name)) result += "西北";
    if ("癸".contains(gzdate.d.gan.name)) result += "西方";
    return result;
  }

  /// 財神方位歌
  /// 甲乙東北是財神，丙丁向在西方尋；
  /// 戊己正北坐方位，庚辛正東去安身；
  /// 壬癸原來正南坐，便是財神方位真。
  String get god3Direction {
    var result = "財神";
    if ("甲乙".contains(gzdate.d.gan.name)) result += "東北";
    if ("丙丁".contains(gzdate.d.gan.name)) result += "西方";
    if ("戊己".contains(gzdate.d.gan.name)) result += "北方";
    if ("庚辛".contains(gzdate.d.gan.name)) result += "東方";
    if ("壬癸".contains(gzdate.d.gan.name)) result += "南方";
    return result;
  }
}
