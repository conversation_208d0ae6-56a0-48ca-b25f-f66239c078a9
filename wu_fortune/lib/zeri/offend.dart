import '../wu_calendar.dart';

/// 沖煞
class Offend {
  static const directionStr = "西南東北";

  final _baseTime = DateTime(1900, 1, 1);
  final DateTime solar;

  Offend(this.solar);

  int get age => (60 - (solar.difference(_baseTime).inDays + 23) % 60) + 1;

  String get zodiac => ZODIAC[(solar.difference(_baseTime).inDays + 4) % 12];

  String get direction => directionStr[(solar.difference(_baseTime).inDays + 3) % 4];

  @override
  String toString() {
    return "沖$zodiac$age歲煞$direction";
  }
}
