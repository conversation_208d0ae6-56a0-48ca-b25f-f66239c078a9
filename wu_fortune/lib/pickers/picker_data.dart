import '../yijing/yijing.dart';

class PickerData {
  final String name; // 取卦方法
  final String note; // 取卦細節
  final String sign6; // 六爻
  PickerData({
    this.name = '',
    this.note = '',
    this.sign6 = '',
  });

  bool get isOneYaoDong {
    // print(pickerData?.name);
    final ret = ['銅錢牌', '銅錢手', "四卡牌", "四滾輪"].contains(name) == false;
    return ret;
  }

  String get yijingName => Yijing(sign6).name;
}
