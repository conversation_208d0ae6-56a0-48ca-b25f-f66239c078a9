import 'package:flutter/material.dart';
import 'package:wu_core/wu_public.dart';
import 'package:wu_fortune/wu_yijing.dart';

import 'picker_controller.dart';

final _cardGroup = [
  Gua8.names,
  Gua8.names,
  Gua8.names,
  Gua8.names,
];

/// 卡牌4數八卦起卦
class CardNum4 extends StatefulWidget {
  const CardNum4({super.key});

  @override
  State<CardNum4> createState() => _CardNum4State();
}

class _CardNum4State extends State<CardNum4> {
  final List<String> result = [];

  void updatePickerData() {
    final controller = Get.find<PickerController>();
    final yijing = Yijing.byGuaName(result[0] + result[1], result[2] + result[3]);
    controller.pickerData = PickerData(
      name: '四卡牌',
      note: '${result.join()} 得卦 ${yijing.name}',
      sign6: yijing.sign6,
    );
  }

  @override
  Widget build(BuildContext context) {
    var cards = result.length < _cardGroup.length ? List.from(_cardGroup[result.length]) : [];

    return Container(
      color: Theme.of(context).colorScheme.secondaryContainer,
      padding: const EdgeInsets.only(top: 8, left: 8, right: 8),
      child: GetBuilder<PickerController>(builder: (controller) {
        cards.shuffle();
        return Column(
          spacing: 8,
          children: [
            ResultTile(
                height: 56,
                child: Row(
                  spacing: 2,
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: result.map((e) => Text(e, style: TextStyle(fontSize: 26))).toList(),
                )),
            if (controller.isRolled == false)
              CardDrewer(
                redraw: true,
                flipSpeed: 400,
                cardCount: cards.length,
                frontBuilder: (index, cardWidth) {
                  return Cards.bagua(cards[index], width: cardWidth);
                },
                backBuilder: (index, cardWidth) {
                  return Stack(
                    alignment: Alignment.center,
                    children: [
                      Cards.back(width: cardWidth),
                      Text(
                        (index + 1).toString(),
                        style: TextStyle(
                          fontFamily: "Arial",
                          fontSize: 32,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  );
                },
                cardSide: (index) => CardSide.BACK,
                onFlipDone: (index) {
                  result.add(cards[index]);
                  if (result.length == _cardGroup.length) {
                    updatePickerData();
                  }
                  setState(() {});
                },
              ),
          ],
        );
      }),
    );
  }
}
