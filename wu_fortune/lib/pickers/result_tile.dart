import 'package:flutter/material.dart';

typedef ResultBuilder = Widget Function();

class ResultTile extends StatelessWidget {
  final double height;
  final Widget child;
  final Color backColor;
  final Color foreColor;
  final Color borderColor;
  const ResultTile({
    super.key,
    this.height = 54,
    required this.child,
    this.backColor = Colors.white,
    this.foreColor = Colors.black,
    this.borderColor = Colors.black,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      decoration: BoxDecoration(
        color: backColor,
        border: Border.all(color: borderColor, width: 1),
        borderRadius: BorderRadius.circular(5),
      ),
      padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 4),
      alignment: Alignment.centerRight,
      child: child,
    );
  }
}
