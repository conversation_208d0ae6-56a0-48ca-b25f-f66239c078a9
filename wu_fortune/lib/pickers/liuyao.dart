import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:wu_fortune/wu_yijing.dart';

import 'picker_controller.dart';

Widget _signImage(String sign, {Color foreColor = Colors.black}) {
  return SvgPicture.asset(
    'assets/images/signs/sign${sign.toLowerCase()}.svg',
    colorFilter: ColorFilter.mode(foreColor, BlendMode.srcIn),
  );
}

Widget _buttonImage(String sign, {Color foreColor = Colors.white}) {
  return SvgPicture.asset(
    'assets/images/buttons/wbut${sign.toLowerCase()}.svg',
    colorFilter: ColorFilter.mode(foreColor, BlendMode.srcIn),
  );
}

/// 六爻起卦
class Liuyao extends StatefulWidget {
  const Liuyao({super.key});

  @override
  State<Liuyao> createState() => _LiuyaoState();
}

class _LiuyaoState extends State<Liuyao> {
  String result = '';

  void addResult(String newSign) {
    if (result.length == 6) return;
    result = newSign + result;
    setState(() {});
    updatePickerData();
  }

  void removeResult() {
    if (result.isEmpty) return;
    result = result.substring(0, result.length - 1);
    setState(() {});
    updatePickerData();
  }

  void updatePickerData() {
    if (result.length != 6) return;
    final controller = Get.find<PickerController>();
    controller.pickerData = PickerData(
      name: '銅錢手',
      note: '$result 得卦 ${Yijing(result).name}',
      sign6: result,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        color: Theme.of(context).colorScheme.secondaryContainer,
        padding: const EdgeInsets.only(top: 8, left: 8, right: 8),
        child: GetBuilder<PickerController>(builder: (controller) {
          final signList = result.split('');
          return Column(
            spacing: 8,
            children: [
              ResultTile(
                  height: 56,
                  child: Row(
                    spacing: 2,
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: signList.map((e) => _signImage(e)).toList(),
                  )),
              buildButtonTile(),
            ],
          );
        }),
      ),
    );
  }

  Widget buildButtonTile() {
    final backColor = const Color.fromARGB(255, 10, 34, 46);
    final delColor = Colors.deepOrange;
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      spacing: 5,
      children: [
        ImageButton(
          backgroundColor: backColor,
          child: _buttonImage('x'),
          onPressed: () => addResult('x'),
        ),
        ImageButton(
          backgroundColor: backColor,
          child: _buttonImage('-'),
          onPressed: () => addResult('-'),
        ),
        ImageButton(
          backgroundColor: backColor,
          child: _buttonImage('='),
          onPressed: () => addResult('='),
        ),
        ImageButton(
          backgroundColor: backColor,
          child: _buttonImage('o'),
          onPressed: () => addResult('o'),
        ),
        ImageButton(
          backgroundColor: delColor,
          child: Icon(Icons.backspace, color: Colors.white),
          onPressed: () => removeResult(),
        ),
      ],
    );
  }
}

class ImageButton extends StatelessWidget {
  final Widget child;
  final Color? backgroundColor;
  final VoidCallback? onPressed;
  final double size;
  const ImageButton({
    super.key,
    required this.child,
    this.onPressed,
    this.backgroundColor,
    this.size = 60,
  });

  @override
  Widget build(BuildContext context) {
    final backColor = backgroundColor ?? Theme.of(context).colorScheme.primary;
    return InkWell(
      onTap: onPressed,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: backColor,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: backColor.withAlpha(200),
              blurRadius: 6,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: child,
      ),
    );
  }
}
