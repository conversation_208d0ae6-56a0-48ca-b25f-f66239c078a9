import 'package:flutter/material.dart';
import 'package:wu_core/wu_public.dart';
import 'package:wu_fortune/wu_yijing.dart';

import 'picker_controller.dart';

const _diceNames = ["一", "二", "三", "四", "五", "六"];

final _cardGroup = [
  Gua8.names,
  Gua8.names,
  _diceNames,
];

/// 三滾輪起卦
class WheelNum3 extends StatefulWidget {
  const WheelNum3({super.key});

  @override
  State<WheelNum3> createState() => _WheelNum3State();
}

class _WheelNum3State extends State<WheelNum3> {
  late final List<List<WheelItem>> wheelsItems;
  List<int> result = [];

  void updatePickerData() {
    final controller = Get.find<PickerController>();
    final yijing = Yijing.byNum3(result[0], result[1], result[2]);
    final names = List.generate(3, (index) => _cardGroup[index][result[index]]).join();
    controller.pickerData = PickerData(
      name: '三數輪',
      note: '$names 得卦 ${yijing.name}',
      sign6: yijing.sign6,
    );
  }

  @override
  void initState() {
    super.initState();
    wheelsItems = _cardGroup.map((group) => group.map((e) => WheelItem(Text(e), e)).toList()).toList();
    result = List.generate(_cardGroup.length, (index) => 0);
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(_cardGroup.length, (index) {
              return WheelPicker(
                items: wheelsItems[index],
                width: 60,
                onSelectedItemChanged: (value) => wheelChanged(index, value),
              );
            }),
          ),
        ),
      ),
    );
  }

  void wheelChanged(int index, String value) {
    result[index] = _cardGroup[index].indexOf(value);
    updatePickerData();
  }
}
