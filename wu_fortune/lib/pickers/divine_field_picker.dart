import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'card_coins.dart';
import 'card_num3.dart';
import 'card_num4.dart';
import 'liuyao.dart';
import 'picker_controller.dart';
import 'text_num3.dart';
import 'wheel_num3.dart';
import 'wheel_num4.dart';

final _viewList = {
  "多爻動": {
    "銅錢牌": const Card<PERSON>oins(),
    "銅錢手": const <PERSON><PERSON><PERSON>(),
    "四卡牌": const CardNum4(),
    "四滾輪": const WheelNum4(),
  },
  "單爻動": {
    "三卡牌": const CardNum3(),
    "三數": const TextNum3(),
    "三滾輪": const WheelNum3(),
  },
};

class DivineFieldPicker extends StatefulWidget {
  final String? question;
  const DivineFieldPicker({super.key, this.question});

  @override
  State<DivineFieldPicker> createState() => _DivineFieldPickerState();
}

class _DivineFieldPickerState extends State<DivineFieldPicker> {
  String level1 = '';
  String level2 = '';

  @override
  void initState() {
    level1 = _viewList.keys.first;
    level2 = _viewList[level1]!.keys.first;
    Get.put(PickerController());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      appBar: AppBar(
        title: const Text('起卦'),
        actions: [
          GetBuilder<PickerController>(
            builder: (controller) {
              return TextButton(
                style: TextButton.styleFrom(
                  disabledForegroundColor: Colors.grey,
                  foregroundColor: colorScheme.onPrimary,
                ),
                onPressed: controller.isRolled ? () => Get.back(result: controller.pickerData) : null,
                child: Text("確定"),
              );
            },
          )
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          spacing: 8,
          children: [
            if (widget.question != null)
              Text(
                widget.question ?? "",
                style: Get.textTheme.headlineSmall,
                textAlign: TextAlign.start,
              ),
            buildSegment(),
            Expanded(child: _viewList[level1]?[level2] ?? Container()),
          ],
        ),
      ),
    );
  }

  Widget buildSegment() {
    return LayoutBuilder(builder: (_, constraints) {
      final width = constraints.maxWidth * 0.9;
      return Column(
        spacing: 8,
        children: [
          SizedBox(
            width: width,
            child: CupertinoSegmentedControl<String>(
                groupValue: level1,
                children: {for (var e in _viewList.keys) e: Text(e)},
                onValueChanged: (value) {
                  level1 = value;
                  level2 = _viewList[level1]!.keys.first;
                  setState(() {});
                }),
          ),
          SizedBox(
            width: width,
            child: CupertinoSegmentedControl<String>(
                groupValue: level2,
                children: {for (var e in _viewList[level1]!.keys) e: Text(e)},
                onValueChanged: (value) {
                  level2 = value;
                  setState(() {});
                }),
          ),
        ],
      );
    });
  }
}
