// import '../controllers/setting_controller.dart';

import 'package:wu_fortune/wu_fortune.dart';

import '../yijing/yijing.dart';

typedef GzDict = Map<String, GanZhi>;

abstract class ReaderBase {
  ReaderBase();

  /// 解讀方式的特點
  String get feature;

  /// 系統提示詞
  String get systemPrompt;

  /// 使用者提示詞
  String userPrompt(GzDict gzDict, Yijing yijing, String question);

  /// 取出 摘要 內容
  String getSummary(String response) {
    final pattern = RegExp(r'## 摘要\n([\s\S]*?)(?=\n##|\Z)'); // 捕獲摘要內容
    final match = pattern.firstMatch(response); // 尋找匹配的部分
    final ret = match?.group(1) ?? response; // 取出群組 1 或原始字串
    return ret.replaceAll('\n', '').trim();
  }

  /// 取出 markdown 內容
  String getMarkdownInner(String response) {
    final pattern = RegExp(r'```markdown\n([\s\S]*?)```'); // 捕獲 markdown 內容
    final match = pattern.firstMatch(response); // 尋找匹配的部分
    return match?.group(1) ?? response; // 取出群組 1 或原始字串
  }

  /// 取出 markdown 內容, qwen/qwen3-32b 不太穩定
  String getLongTextInner(String response) {
    final pattern = RegExp(r'```\n([\s\S]*?)```'); // 捕獲 markdown 內容
    final match = pattern.firstMatch(response); // 尋找匹配的部分
    return match?.group(1) ?? response; // 取出群組 1 或原始字串
  }

  /// 取出 JSON 內容
  String getJsonInner(String response) {
    final pattern = RegExp(r'```json\n([\s\S]*?)```'); // 捕獲 JSON 內容
    final match = pattern.firstMatch(response); // 尋找匹配的部分
    return match?.group(1) ?? response; // 取出群組 1 或原始字串
  }

  /// 移除<think></think>之間的內容
  String removeThink(String response) {
    return response.replaceAll(RegExp(r'<think>[\s\S]*?</think>'), '');
  }
}
