part of 'readers.dart';

class <PERSON>Gaoda<PERSON> extends ReaderBase {
  ReaderGaodao();

  @override
  String get feature => '''
## 高島吞象解讀
### 內容：
日本明治時代的易學家高島吞象，將卦象與歷史人物、事件對應，建立通俗而具體的象徵解釋。

### 目的：
提供直觀的卦意解釋，可作為占卜輔助。

### 特色：
- 每卦附有故事、名人事例。
- 有明顯的吉凶判斷與建議。
- 適合初學者快速理解卦意。
''';

  @override
  String get systemPrompt => """
你是一位熟讀高島流案例且社會經驗豐富的易經老師
- 用簡單易懂的方式解讀卦象
- 回答要口語化，像朋友聊天一樣。
- 請直接描述結果，不要分析過程。
- 不要有前綴的描述或提示詞。

格式：
```markdown
# 高島流解讀

## 摘要
(用30字以內總結卦象的狀況)

## 本卦 - [卦名]
(描述現在的狀況)

## 變爻 - [第幾爻]
(描述需要注意的變化)

## 變卦 - [卦名]
(描述最終結果)

## 建議
(描述該怎麼做，並提供適當的建議)
```
""";

  @override
  String userPrompt(GzDict gzDict, Yijing yijing, String question) {
    final gzDateStr = gzDict.entries.map((e) => "${e.value.name}${e.key}").join('');
    String userPrompt = """
---
請為下列內容解讀卦象：
**占卦時間：** ${gzDateStr}
**問題：** ${question}
**本卦：** ${Gua64.bySign6(yijing.orginal).fullname}
**變卦：** ${Gua64.bySign6(yijing.changed).fullname}
**變爻：** ${yijing.dongYao.last}
""";
    return userPrompt;
  }
}
