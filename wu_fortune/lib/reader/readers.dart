import 'package:wu_fortune/wu_yijing.dart';

import 'reader_base.dart';

part 'reader_gaodao.dart';
part 'reader_tiyong.dart';
part 'reader_liuyao.dart';
part 'reader_zhouyi.dart';
part 'reader_zy_simple.dart';

enum ReadingMode {
  zhouyi("周易"), // 易經式解讀
  zySimple("周易精簡"), // 易經式解讀
  gaodao("高島"), // 高島流解讀
  tiyong("體用"), // 體用式解讀
  liuyao("六爻"), // 六爻式解讀
  ;

  final String name;
  const ReadingMode(this.name);

  static ReadingMode byName(String? name) {
    return values.firstWhere((e) => e.name == name, orElse: () => ReadingMode.zhouyi);
  }
}

ReaderBase getReader(ReadingMode mode) {
  return switch (mode) {
    ReadingMode.zhouyi => ReaderZhouyi(),
    ReadingMode.zySimple => ReaderZySimple(),
    ReadingMode.gaodao => ReaderGaodao(),
    ReadingMode.tiyong => ReaderTiyong(),
    ReadingMode.liuyao => ReaderLiuyao(),
  };
}
