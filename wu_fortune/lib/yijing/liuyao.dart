import '../wu_calendar.dart';
import 'liuyao_gods.dart';
import 'yijing.dart';

const YAO_NAMES = ["初爻", "二爻", "三爻", "四爻", "五爻", "上爻"];

const SHIYAO_DICT = {
  "八純": 5, "一世": 0, "二世": 1, "三世": 2, //
  "四世": 3, "五世": 4, "遊魂": 3, "歸魂": 2, //
};

class Liuyao {
  final Yijing yijing;
  final GanZhi m;
  final GanZhi d;

  Liuyao(this.yijing, this.m, this.d) {
    _initialize();
  }

  late final List<LiuyaoRow> rows = List.generate(6, (index) => LiuyaoRow(YAO_NAMES[index]));
  late final List<Gua64> gua64s = _buildGua64s();
  late final int shiyaoIndex;
  late final int yingyaoIndex;
  late final Map<String, String> shensha = {};

  List<Gua64> _buildGua64s() {
    final org = yijing.gua64s["本卦"]!;
    final ret = [
      Gua64.byName(org.gong + org.gong),
      yijing.gua64s["本卦"]!,
      yijing.gua64s["變卦"]!,
    ];
    return ret;
  }

  void _initialize() {
    final orginal = yijing.orginal;

    // 找世爻
    shiyaoIndex = SHIYAO_DICT[gua64s[0].dai]!;
    yingyaoIndex = (shiyaoIndex + 3) % 6;

    // 安六獸
    _buildShou();

    // 填充符號
    for (var i = 0; i < 6; i++) {
      rows[i].sign = yijing.sign6[i];
    }

    // 填充爻位信息
    final qinCollect = <String>{};
    for (var i = 0; i < 6; i++) {
      // 本卦
      rows[i].cells[1].zhi = Zhi.byName(gua64s[0].layout[i]);
      rows[i].cells[1].qin = _getQin(gua64s[0].layout[i]);
      qinCollect.add(rows[i].cells[1].qin);

      // 變卦
      rows[i].cells[2].zhi = Zhi.byName(gua64s[1].layout[i]);
      rows[i].cells[2].qin = _getQin(gua64s[1].layout[i]);

      // 伏卦
      rows[i].cells[0].zhi = Zhi.byName(gua64s[2].layout[i]);
      rows[i].cells[0].qin = _getQin(gua64s[2].layout[i]);
    }

    // 處理無用的伏爻和變爻
    for (var i = 0; i < 6; i++) {
      // 移除無用的伏爻
      if (qinCollect.contains(rows[i].cells[0].qin)) {
        rows[i].cells[0].enabled = false;
      }

      // 移除無用的變爻
      if (rows[i].sign == "-" || rows[i].sign == "=") {
        rows[i].cells[2].enabled = false;
      }
    }

    // Create a map with Zhi names as keys and empty string lists as values
    final zhiDict = {for (var e in Zhi.names) e: <String>[]};
    // 旬空
    for (var e in d.xunkong.split("")) {
      zhiDict[e]?.add("旬空");
    }
    // 日月建
    zhiDict[m.zhi.name]?.add("月建");
    zhiDict[d.zhi.name]?.add("日建");
    zhiDict[Zhi.byIndex(m.zhi.index + 6).name]?.add("月破");
    // 日支沖的地支，旺相為日動，休囚為日破
    final anZhi = Zhi.byIndex(d.zhi.index + 6);
    final anSk = ShengKe.byCompare(m.zhi, anZhi);
    zhiDict[d.zhi.name]?.add(anSk.score < 2 ? "日動" : "日破");
    // 卦身爻
    var yy = orginal[shiyaoIndex] == '-' ? 0 : 6;
    zhiDict[Zhi.byIndex(yy + shiyaoIndex).name]?.add("身爻");

    // 神煞信息
    final gods = LiuyaoGods.getList(d);
    for (var god in gods.entries) {
      shensha[god.key] = god.value;
      zhiDict[god.value]?.add(god.key);
    }
  }

  // 計算地支相對於宮卦的六親
  String _getQinRelativeToGong(Zhi zhi) {
    final gong = gua64s[0].gua8s[0]; // 宮卦的內卦
    final sk = ShengKe.byCompare(zhi, gong);
    return sk.liuqin;
  }

  // 計算回頭生剋
  String _getHuitouShengKe(Zhi originalZhi, Zhi transformedZhi) {
    final sk = ShengKe.byCompare(transformedZhi, originalZhi);
    if (sk.score > 0) {
      return "回頭生";
    } else if (sk.score < 0) {
      return "回頭剋";
    } else {
      return "回頭比和"; // Or just "" if not needed
    }
  }

  // 安六獸
  // 甲乙起青龍，丙丁起朱雀，戊起勾陳，己起騰蛇，庚辛起白虎，壬癸起玄武。
  // 順序由下往上裝，青龍→朱雀→勾陳→騰蛇→白虎→玄武。
  void _buildShou() {
    final shouList = "青朱勾蛇白玄" * 2;
    final ganidx = [0, 0, 1, 1, 2, 3, 4, 4, 5, 5];
    final shouIndex = ganidx[d.gan.index];
    for (var i = 0; i < 6; i++) {
      rows[5 - i].shou = shouList[shouIndex + i];
    }
  }

  // 安六親
  String _getQin(String zhi) {
    final gong = gua64s[2].gua8s[0];
    final sk = ShengKe.byCompare(Zhi.byName(zhi), gong);
    return sk.liuqin;
  }

  String toPrompt(String question) {
    final buffer = StringBuffer();

    buffer.writeln("**六爻占卜分析數據：**");
    buffer.writeln("**時間：** ${m.name}月${d.name}日");
    buffer.writeln("**問題：** $question");

    // Determine 事神 六親 type and 事神爻 index
    // Assuming 事神 is "官鬼爻" for now based on sample
    final yongShenQin = "官鬼";
    int yongShenIndex = -1;
    for (var i = 0; i < 6; i++) {
      if (rows[i].cells[1].qin == yongShenQin) {
        yongShenIndex = i;
        break; // Take the first one
      }
    }
    buffer.writeln("**用神：** 本次占卜的事神是**${yongShenQin}爻**");

    buffer.writeln("**卦象：** 本卦 [${gua64s[1].name}] 變卦 [${gua64s[2].name}]");

    buffer.writeln("**各爻詳情及關係：**");
    for (var i = 0; i < 6; i++) {
      final row = rows[i];
      final originalYao = row.cells[1];
      final transformedYao = row.cells[2];
      final fuyinYao = row.cells[0];

      final yaoName = YAO_NAMES[i];
      final originalZhi = originalYao.zhi!;
      final originalWuxing = originalZhi.wuxing.name;
      final originalQin = originalYao.qin;
      final yaoState = ShengKe.byCompare(m.zhi, originalZhi).wangxiang;
      final dongJing = (row.sign == 'o' || row.sign == 'x') ? "動爻" : "靜爻";

      String yaoDetails =
          "* **$yaoName:** ${originalZhi.name}，五行-${originalWuxing}。六親-${originalQin}。狀態：${yaoState}。${dongJing}。";

      // Add 世應
      if (i == shiyaoIndex) {
        yaoDetails += "世爻。";
      }
      if (i == yingyaoIndex) {
        yaoDetails += "應爻。";
      }

      // Add 事神標記
      if (i == yongShenIndex) {
        yaoDetails += "事神。";
      }

      // Add 日月建標記
      if (originalZhi.name == m.zhi.name) {
        yaoDetails += "月建。";
      }
      if (originalZhi.name == d.zhi.name) {
        yaoDetails += "日建。";
      }

      buffer.writeln(yaoDetails);

      // Add 伏爻
      if (fuyinYao.enabled && fuyinYao.zhi != null) {
        final fuyinZhi = fuyinYao.zhi!;
        final fuyinWuxing = fuyinZhi.wuxing.name;
        final fuyinQin = fuyinYao.qin;
        buffer.writeln("    * **伏爻：** ${fuyinZhi.name}，五行-${fuyinWuxing}。六親-${fuyinQin}。");
      }

      // Add 化爻
      if (transformedYao.enabled && transformedYao.zhi != null) {
        final transformedZhi = transformedYao.zhi!;
        final transformedWuxing = transformedZhi.wuxing.name;
        final transformedQin = transformedYao.qin;
        final huitou = _getHuitouShengKe(originalZhi, transformedZhi);
        buffer.writeln("    * **化爻：** ${transformedZhi.name}，五行-${transformedWuxing}。六親-${transformedQin}，${huitou}。");
      }
    }

    buffer.writeln("**月建、日建**");
    buffer.writeln("* 月建：${m.zhi.name}${m.zhi.wuxing.name}${_getQinRelativeToGong(m.zhi)}");
    buffer.writeln("* 日建：${d.zhi.name}${d.zhi.wuxing.name}${_getQinRelativeToGong(d.zhi)}");

    buffer.writeln("**事神與世應關係：**");
    if (yongShenIndex != -1) {
      final yongShenYao = rows[yongShenIndex].cells[1];
      buffer.writeln("* 事神${rows[yongShenIndex].name}${yongShenYao.zhi!.name}${yongShenYao.zhi!.wuxing.name}");
    } else {
      buffer.writeln("* 未找到事神爻 (${yongShenQin})");
    }

    final shiyaoYao = rows[shiyaoIndex].cells[1];
    buffer.writeln("* 世爻${rows[shiyaoIndex].name}${shiyaoYao.zhi!.name}${shiyaoYao.zhi!.wuxing.name}");

    final yingyaoYao = rows[yingyaoIndex].cells[1];
    String yingYaoLine = "* 應爻${rows[yingyaoIndex].name}${yingyaoYao.zhi!.name}${yingyaoYao.zhi!.wuxing.name}";
    if (yingyaoIndex == yongShenIndex) {
      yingYaoLine += "同事神。";
    }
    buffer.writeln(yingYaoLine);

    buffer.writeln("**動爻與用神關係：**");
    for (var i = 0; i < 6; i++) {
      final row = rows[i];
      final originalYao = row.cells[1];
      final transformedYao = row.cells[2];

      // Check if it's a moving yao with a transformed yao
      if ((row.sign == 'o' || row.sign == 'x') && transformedYao.enabled && transformedYao.zhi != null) {
        final huitou = _getHuitouShengKe(originalYao.zhi!, transformedYao.zhi!);
        if (huitou == "回頭剋") {
          buffer.writeln("* ${rows[i].name}化回頭剋，無效");
        }
        // Add other relationships if needed, based on sample. Sample only shows 回頭剋.
      }
    }

    return buffer.toString();
  }
}

class LiuyaoRow {
  final String name;
  String shou = '';
  String sign = '';
  late final List<LiuyaoCell> cells = List.generate(3, (_) => LiuyaoCell());

  LiuyaoRow(this.name);
}

class LiuyaoCell {
  Zhi? zhi;
  String qin = '';
  bool enabled = true;
}
