import 'package:wu_fortune/wu_calendar.dart';

import '../wu_fortune.dart';

const YAO_NAMES = "上五四三二初";

class Yijing {
  /// 六爻符號，左邊為上爻，-陽=陰o老陽x老陰
  /// 搖出得●◎◎，一背為單，畫─。得●●◎，二背為折，畫=。得●●●，三背為重，畫o。得◎◎◎，三面為交，畫x。
  final String _sign6;
  String get sign6 => _sign6;

  // Use final instead of late
  Yijing(String sign6) : _sign6 = sign6.toLowerCase();

  /// 梅花易數專用的三數起卦法，三數都從零開始
  factory Yijing.byNum3(int num1, int num2, int num3) {
    final signs = (Gua8.byIndex(num1).sign + Gua8.byIndex(num2).sign).split('');
    num3 = num3 % 6;
    final index = 5 - num3;
    if (signs[index] == "=") signs[index] = "x";
    if (signs[index] == "-") signs[index] = "o";
    return Yijing(signs.join());
  }

  /// 由卦名取得卦象
  factory Yijing.byGuaName(String orginal, String changed) {
    final orginalSign6 = Gua64.byName(orginal).sign6;
    final changedSign6 = Gua64.byName(changed).sign6;
    final sign6 = orginalSign6.split("");
    for (var i = 0; i < 6; i++) {
      if (orginalSign6[i] != changedSign6[i]) {
        sign6[i] = (orginalSign6[i] == "-") ? "o" : "x";
      }
    }
    return Yijing(sign6.join());
  }

  /// 卦名稱 - 使用緩存提高性能
  late final String name = _computeName();

  String _computeName() {
    final org = gua64s["本卦"]!;
    final chg = gua64s["變卦"]!;
    return org.name == chg.name ? org.fullname : "${org.xiang}之${chg.xiang}卦";
  }

  /// 爻名符號對照 - 使用緩存提高性能
  late final Map<String, String> yaos = _computeYaos();

  Map<String, String> _computeYaos() {
    final signs = _sign6.split("");
    final ret = <String, String>{};
    for (var i = 0; i < 6; i++) {
      ret[YAO_NAMES[i]] = signs[i];
    }
    return ret;
  }

  /// 靜爻 - 使用緩存提高性能
  late final List<String> jingYao = yaos.entries.where((e) => "-=".contains(e.value)).map((e) => e.key).toList();

  /// 動爻 - 使用緩存提高性能
  late final List<String> dongYao = yaos.entries.where((e) => "ox".contains(e.value)).map((e) => e.key).toList();

  /// 緩存各種卦象
  late final Map<String, Gua64> gua64s = {
    "本卦": Gua64.bySign6(orginal),
    "變卦": Gua64.bySign6(changed),
    "互卦": Gua64.bySign6(_interaction()),
    "錯卦": Gua64.bySign6(_swap()),
    "綜卦": Gua64.bySign6(_reverse()),
  };

  /// 原卦：代表的是目前的情況。
  late final String orginal = _computeOrginal();

  String _computeOrginal() {
    var ret = _sign6;
    ret = ret.replaceAll("o", "-");
    ret = ret.replaceAll("x", "=");
    return ret;
  }

  /// 變卦：事情發展的結果。
  late final String changed = _computeChanged();

  String _computeChanged() {
    var ret = _sign6;
    ret = ret.replaceAll("o", "=");
    ret = ret.replaceAll("x", "-");
    return ret;
  }

  /// **互卦** 表示提問事情發展的過程
  /// 上卦：本卦之第三、四、五爻，下卦：本卦之第二、三、四爻。乾坤無互，改用變卦
  String _interaction() {
    // 乾坤無互，改用變卦
    var ret = (["------", "======"].contains(orginal)) ? changed : orginal;
    // 取互卦公式，543爲上卦，432爲下卦，左邊為上爻，右邊為下爻
    ret = ret.substring(1, 4) + ret.substring(2, 5);
    return ret;
  }

  /// **錯卦** 從問題的對立面來分析問題。
  /// 將本卦各爻位置上的陰陽互換
  String _swap() {
    var ret = orginal.split("");
    for (var i = 0; i < _sign6.length; i++) {
      ret[i] = ret[i] == "-" ? "=" : "-";
    }
    return ret.join("");
  }

  /// ## **綜卦** 從其他的角度分析問題。
  /// 將本卦各爻位置順序，上下顛倒重置(如：將初、二、三、四、五、上爻位置顛倒，
  /// 重新排列而成上、五、四、三、二、初爻)，但陰陽不變，所得出之卦，就稱之為本卦的綜卦。
  String _reverse() {
    var ret = orginal.split("").reversed.join();
    return ret;
  }
}
