import 'package:flutter/material.dart';

import '../wu_calendar.dart';

class <PERSON>ile<PERSON>ieqi extends StatelessWidget {
  final int year;
  final double textScale;
  const <PERSON><PERSON><PERSON><PERSON><PERSON>({super.key, required this.year, this.textScale = 1.0});

  @override
  Widget build(BuildContext context) {
    final current = Jieqi.bySolar(DateTime.now());
    final currColor = Colors.red[200]!;

    Widget buildHeader(String text) {
      return Padding(
        padding: const EdgeInsets.all(2.0),
        child: Text(
          text,
          textScaler: TextScaler.linear(textScale),
          textAlign: TextAlign.center,
        ),
      );
    }

    Widget buildCell(String text, {Color? color}) {
      return Container(
        padding: const EdgeInsets.all(2.0),
        color: color,
        child: Text(
          text,
          textScaler: TextScaler.linear(textScale),
          textAlign: TextAlign.center,
        ),
      );
    }

    final colorScheme = Theme.of(context).colorScheme;

    return Table(
      border: TableBorder.all(color: colorScheme.outlineVariant),
      children: [
        TableRow(
          decoration: BoxDecoration(color: colorScheme.secondaryContainer),
          children: [
            buildHeader("月"),
            buildHeader("節"),
            buildHeader("日"),
            buildHeader("氣"),
            buildHeader("日"),
          ],
        ),
        ...List.generate(12, (month) {
          final monthStr = zhMonthFull[month];
          final jq1 = Jieqi.byYearIndex(year, month * 2);
          final jq1Color = jq1.listIndex == current.listIndex ? currColor : Colors.transparent;
          final jq2 = Jieqi.byYearIndex(year, month * 2 + 1);
          final jq2Color = jq2.listIndex == current.listIndex ? currColor : Colors.transparent;
          return TableRow(children: [
            buildCell(monthStr),
            buildCell(jq1.name, color: jq1Color),
            buildCell(jq1.dateTime.day.toString(), color: jq1Color),
            buildCell(jq2.name, color: jq2Color),
            buildCell(jq2.dateTime.day.toString(), color: jq2Color),
          ]);
        })
      ],
    );
  }
}
