import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class Gua64Render extends StatelessWidget {
  final String sign6;
  final double width;
  const Gua<PERSON><PERSON><PERSON>({super.key, required this.sign6, required this.width});

  @override
  Widget build(BuildContext context) {
    final signs = sign6.split('');
    return SizedBox(
      width: width,
      child: Column(
        children: [
          ...List.generate(signs.length, (index) {
            return buildYao(signs[index]);
          })
        ],
      ),
    );
  }

  Widget buildYao(String sign) {
    String path = ['-', 'o'].contains(sign) ? "陽" : "陰";
    Color color = ['x', 'o'].contains(sign) ? Colors.red : Colors.black;
    return SvgPicture.asset(
      'packages/wu_fortune/assets/signs/${path}.svg',
      colorFilter: ColorFilter.mode(color, BlendMode.srcIn),
    );
  }
}
