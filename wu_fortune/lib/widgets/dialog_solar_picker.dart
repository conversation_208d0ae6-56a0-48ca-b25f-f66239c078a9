import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../wu_calendar.dart';
import '../wu_widgets.dart';

class DialogSolarPicker extends StatefulWidget {
  final DateTime? solar;
  final int startYear;
  final int endYear;
  const DialogSolarPicker({super.key, this.solar, this.startYear = 1900, this.endYear = 2100});

  @override
  State<DialogSolarPicker> createState() => _DialogSolarPickerState();
}

class _DialogSolarPickerState extends State<DialogSolarPicker> {
  int year = 0, month = 0, day = 0, hour = 0;
  final borderSize = BorderSide(width: 1.0, color: Colors.grey);

  @override
  void initState() {
    var solar = widget.solar ?? DateTime.now();
    solar = GzDate.hourStart(solar);
    year = solar.year;
    month = solar.month;
    day = solar.day;
    hour = solar.hour ~/ 2 * 2;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(8)),
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 300),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [_buildButtonRow(), _buildHeaderRow(), _buildMonthCalendar(), _buildHourPicker()],
          ),
        ),
      ),
    );
  }

  Widget _buildButtonRow() {
    final colorScheme = Get.theme.colorScheme;
    return OverflowBar(
      spacing: 8,
      children: [
        ElevatedButton(
          onPressed: () {
            Get.back();
          },
          child: Text("取消"),
        ),
        ElevatedButton(
          style: ElevatedButton.styleFrom(backgroundColor: colorScheme.primary, foregroundColor: colorScheme.onPrimary),
          onPressed: () {
            final solar = DateTime(year, month, day, hour);
            Get.back(result: solar);
          },
          child: const Text("確定"),
        ),
      ],
    );
  }

  Widget _buildHeaderRow() {
    return Row(
      children: [
        TextButton(
          onPressed: () async {
            // 替换当前 dialog 的内容为 WuYearPicker
            final result = await Get.dialog(
              DialogYearPicker(startYear: widget.startYear, endYear: widget.endYear, selectedYear: year),
            );
            if (result != null) {
              setState(() => year = result);
            }
          },
          child: Text("${year}年"),
        ),
        DropdownButton(
          focusColor: Colors.transparent,
          value: month,
          items: List.generate(
            12,
            (index) => DropdownMenuItem(value: index + 1, child: Text("${zhMonthFull[index]}月")),
          ),
          onChanged: (value) => setState(() => month = value ?? 0),
        ),
        Spacer(),
        TextButton(
          onPressed: () {
            final solar = GzDate.hourStart(DateTime.now());
            setState(() {
              year = solar.year;
              month = solar.month;
              day = solar.day;
              hour = solar.hour;
              // 移除未使用的变量
            });
          },
          child: Text("現在"),
        ),
      ],
    );
  }

  Widget _buildMonthCalendar() {
    return Container(
      decoration: BoxDecoration(border: Border(bottom: borderSize, right: borderSize)),
      child: Column(children: [_buildWeekRow(), _buildCalendarGrid()]),
    );
  }

  Widget _buildWeekRow() {
    return Row(
      children: List.generate(7, (index) {
        final tcolor = (index == 0 || index == 6) ? Colors.red : Colors.black;
        return Expanded(
          child: Container(
            decoration: BoxDecoration(border: Border(top: borderSize, left: borderSize)),
            alignment: Alignment.center,
            padding: EdgeInsets.symmetric(vertical: 4),
            child: Text(zhWeek[index], style: TextStyle(color: tcolor, height: 1.0)),
          ),
        );
      }),
    );
  }

  Widget _buildCalendarGrid() {
    var firstDay = DateTime(year, month, 1);
    firstDay = firstDay.subtract(Duration(days: firstDay.weekday - 1));

    return GridView.count(
      crossAxisCount: 7,
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      children: List.generate(42, (index) {
        final date = firstDay.add(Duration(days: index));
        Color ccolor = Colors.grey;
        if (date.month == month) {
          ccolor = Colors.black;
          if (date.weekday == 1 || date.weekday == 7) {
            ccolor = Colors.red;
          }
        }
        Widget cell = Text(date.day.toString(), style: TextStyle(color: ccolor, height: 1.0));
        if ((date.year == year) && (date.month == month) && (date.day == day)) {
          cell = CircleAvatar(backgroundColor: Colors.blue[200]!, child: cell);
        }
        cell = Container(
          height: 40,
          decoration: BoxDecoration(border: Border(top: borderSize, left: borderSize)),
          alignment: Alignment.center,
          child: cell,
        );
        if (date.month == month) {
          cell = InkWell(onTap: () => setState(() => day = date.day), child: cell);
        }
        return cell;
      }),
    );
  }

  Widget _buildHourPicker() {
    return Container(
      decoration: BoxDecoration(border: Border(bottom: borderSize, right: borderSize)),
      child: GridView.count(
        crossAxisCount: 6,
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        children: List.generate(12, (index) {
          final showHour = (index * 2) % 24;
          Widget cell = ZhiRender(zhi: Zhi.byName(ZHIS[index]));
          if (hour == showHour) {
            cell = CircleAvatar(backgroundColor: Colors.blue[200]!, child: cell);
          }

          return InkWell(
            onTap: () => setState(() => hour = showHour),
            child: Container(
              height: 40,
              decoration: BoxDecoration(border: Border(top: borderSize, left: borderSize)),
              alignment: Alignment.center,
              child: cell,
            ),
          );
        }),
      ),
    );
  }
}
