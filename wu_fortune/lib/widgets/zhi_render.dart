import 'package:flutter/material.dart';
import 'package:wu_core/wu_extensions.dart';

import '../wu_fortune.dart';

final goodColor = Colors.red;
final badColor = Colors.green;

class ZhiRender extends StatelessWidget {
  final Zhi zhi;
  final Zhi? zhiM, zhiD;
  final double textScale;
  final bool showHour, showScore, showColor, showExt;
  final bool enabled;
  const ZhiRender({
    super.key,
    required this.zhi,
    this.textScale = 1.0,
    this.zhiM,
    this.zhiD,
    this.showColor = false,
    this.showHour = false,
    this.showScore = false,
    this.showExt = false,
    this.enabled = true,
  });

  Widget buildScore(int score, int fontAlpha) {
    score = score - 2;
    final color = score > 0 ? goodColor : badColor;
    return Text(
      score.abs().toString(),
      style: TextStyle(color: color.withAlpha(fontAlpha), fontSize: 9, height: 1.0),
    );
  }

  @override
  Widget build(BuildContext context) {
    final fontAlpha = enabled ? 255 : 80;
    final scoreM = zhiM == null ? 0 : ShengKe.byCompare(zhiM!, zhi).score;
    final scoreD = zhiD == null ? 0 : ShengKe.byCompare(zhiD!, zhi).score;
    var textColor = showColor ? zhi.color : Colors.black;
    textColor = textColor.withAlpha(fontAlpha);
    final TextStyle goodStyle = TextStyle(color: goodColor.withAlpha(fontAlpha), fontSize: 9, height: 1.0);
    final TextStyle badStyle = TextStyle(color: badColor.withAlpha(fontAlpha), fontSize: 9, height: 1.0);
    final extM = <Widget>[];
    if (zhiM != null) {
      if (zhi.index == zhiM?.index) {
        extM.add(Text("月", style: goodStyle));
      }
      if ((zhi.index + 6) % 12 == zhiM?.index) {
        extM.add(Text("破", style: badStyle));
      }
    }
    final extD = <Widget>[];
    if (zhiD != null) {
      if (zhi.index == zhiD?.index) {
        extD.add(Text("日", style: goodStyle));
      }
      // 1.暗動：爻在旺相的狀態下若被日令所沖，此爻則論被沖起。
      // 2.日破：靜爻在月建條件下不旺，同時又受到日建相沖。
      if ((zhi.index + 6) % 12 == zhiD?.index) {
        if (scoreD > 0) {
          extD.add(Text("暗", style: goodStyle));
        } else {
          extD.add(Text("破", style: badStyle));
        }
      }
    }
    return Padding(
      padding: const EdgeInsets.all(2.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            zhi.name,
            textScaler: TextScaler.linear(textScale),
            style: TextStyle(fontSize: 20, height: 1.0, color: textColor),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Row(
                children: [
                  if (showHour)
                    Text(
                      zhi.hour.strzero(2),
                      textScaler: TextScaler.linear(textScale),
                      style: TextStyle(fontSize: 9, height: 1.0),
                    ),
                  if (showScore && zhiM != null) buildScore(scoreM, fontAlpha),
                  if (showExt) ...extM,
                ],
              ),
              Row(
                children: [
                  if (showHour)
                    Text(
                      zhi.hourM.strzero(2),
                      textScaler: TextScaler.linear(textScale),
                      style: TextStyle(fontSize: 9, height: 1.0),
                    ),
                  if (showScore && zhiD != null) buildScore(scoreD, fontAlpha),
                  if (showExt) ...extD,
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }
}
