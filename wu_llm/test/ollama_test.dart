import 'package:test/test.dart';
import 'package:wu_llm/wu_llm.dart';

void main() {
  group('Ollama LLM Tests', () {
    late LlmOllama ollama;

    setUp(() {
      ollama = LlmOllama();
    });

    test('should create Ollama instance with correct properties', () {
      expect(ollama.serviceName, equals('Ollama'));
      expect(ollama.apiKeyUrl, equals('https://ollama.com/download'));
      expect(ollama.recommandModels, isEmpty);
    });

    test('should cast roles correctly', () {
      expect(ollama.roleCast(LlmRole.system), equals('system'));
      expect(ollama.roleCast(LlmRole.user), equals('user'));
      expect(ollama.roleCast(LlmRole.assistant), equals('assistant'));
    });

    test('should format messages correctly', () {
      final messages = [
        LlmMessage(role: LlmRole.system, content: 'You are a helpful assistant.'),
        LlmMessage(role: LlmRole.user, content: 'Hello!'),
      ];

      final formatted = ollama.formatMessages(messages);
      
      expect(formatted, hasLength(2));
      expect(formatted[0]['role'], equals('system'));
      expect(formatted[0]['content'], equals('You are a helpful assistant.'));
      expect(formatted[1]['role'], equals('user'));
      expect(formatted[1]['content'], equals('Hello!'));
    });

    test('should be accessible through factory method', () {
      final service = LlmBase.getService('Ollama');
      expect(service, isA<LlmOllama>());
      expect(service.serviceName, equals('Ollama'));
    });

    // Note: The following tests require a running Ollama instance
    // Uncomment and modify as needed for integration testing
    
    /*
    test('should get models list', () async {
      try {
        final models = await ollama.getModels('dummy-key');
        expect(models, isA<List<String>>());
        // Models list should contain at least one model if Ollama is running
      } catch (e) {
        // Expected if Ollama is not running
        print('Ollama not available: $e');
      }
    });

    test('should stream text responses', () async {
      final messages = [
        LlmMessage(role: LlmRole.user, content: 'Say hello'),
      ];

      try {
        final stream = ollama.streamText(
          apiKey: 'dummy-key',
          modelName: 'llama2', // Use an available model
          messages: messages,
        );

        final chunks = <String>[];
        await for (final chunk in stream) {
          if (chunk != null) {
            chunks.add(chunk);
          }
        }

        expect(chunks, isNotEmpty);
        print('Received ${chunks.length} chunks');
      } catch (e) {
        // Expected if Ollama is not running or model not available
        print('Ollama streaming test failed: $e');
      }
    });
    */
  });
}
