import 'dart:convert';

import 'package:dio/dio.dart';
import 'llm_message.dart';
import 'llm_base.dart';

/// Groq 服務實現
class LlmGroq extends LlmBase {
  static const String _baseUrl = 'https://api.groq.com/openai/v1';

  LlmGroq()
      : super(
          serviceName: 'Groq',
          recommandModels: [
            'deepseek-r1-distill-llama-70b',
            'qwen-qwq-32b',
            'qwen/qwen3-32b',
            'llama3-8b-8192',
            'llama-3.3-70b-versatile',
          ],
          apiKeyUrl: 'https://console.groq.com/keys',
        );

  @override
  Future<List<String>> getModels(String? apiKey) async {
    if (apiKey == null) return recommandModels;
    final dio = Dio(
      BaseOptions(
        baseUrl: _baseUrl,
        sendTimeout: sendTimeout,
        receiveTimeout: receiveTimeout,
        headers: {'Content-Type': 'application/json', 'Authorization': 'Bearer $apiKey'},
      ),
    );
    final response = await dio.get('/models');

    final data = response.data as Map<String, dynamic>;
    final models = (data['data'] as List).map((model) => model['id'] as String).toList();
    return models;
  }

  @override
  String roleCast(LlmRole role) => switch (role) {
        LlmRole.system => 'assistant',
        LlmRole.user => 'user',
        LlmRole.assistant => 'assistant',
      };

  @override
  Future<String?> futureText({
    required String apiKey,
    required String modelName,
    required List<LlmMessage> messages,
  }) async {
    try {
      final dio = Dio(
        BaseOptions(
          baseUrl: _baseUrl,
          sendTimeout: sendTimeout,
          receiveTimeout: receiveTimeout,
          headers: {'Content-Type': 'application/json', 'Authorization': 'Bearer $apiKey'},
        ),
      );

      final response = await dio.post(
        '/chat/completions',
        data: {'model': modelName, 'messages': formatMessages(messages), 'stream': false},
      );

      final result = response.data['choices'][0]['message']['content'];
      return result ?? '';
    } catch (e) {
      return handleError(e);
    }
  }

  @override
  Stream<String?> streamText({
    required String apiKey,
    required String modelName,
    required List<LlmMessage> messages,
  }) async* {
    try {
      final dio = Dio(
        BaseOptions(
          baseUrl: _baseUrl,
          sendTimeout: sendTimeout,
          receiveTimeout: receiveTimeout,
          headers: {'Content-Type': 'application/json', 'Authorization': 'Bearer $apiKey'},
        ),
      );

      final response = await dio.post(
        '/chat/completions',
        data: {'model': modelName, 'messages': formatMessages(messages), 'stream': true},
        options: Options(responseType: ResponseType.stream),
      );

      final stream = response.data.stream as Stream<List<int>>;
      String buffer = '';

      await for (final chunk in stream) {
        buffer += utf8.decode(chunk);
        final lines = buffer.split('\n');
        buffer = lines.last;

        for (final line in lines) {
          if (line.startsWith('data: ') && line != 'data: [DONE]') {
            final data = line.substring(6);
            try {
              final json = jsonDecode(data);
              final content = json['choices'][0]['delta']['content'] as String?;
              if (content != null) yield content;
            } catch (e) {
              // 忽略解析錯誤
            }
          }
        }
      }
    } catch (e) {
      yield handleError(e);
    }
  }
}
