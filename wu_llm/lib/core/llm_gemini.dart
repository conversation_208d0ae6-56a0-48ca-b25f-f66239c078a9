import 'dart:convert';

import 'package:dio/dio.dart';
import 'llm_message.dart';
import 'llm_base.dart';

/// Gemini 服務實現
class LlmGemini extends LlmBase {
  static const String _baseUrl = 'https://generativelanguage.googleapis.com/v1';

  LlmGemini()
      : super(
          serviceName: 'Gemini',
          apiKeyUrl: 'https://aistudio.google.com/app/apikey',
        );

  @override
  Future<List<String>> getModels(String? apiKey) async {
    if (apiKey == null) return recommandModels;
    try {
      final dio = Dio(
        BaseOptions(
          baseUrl: _baseUrl,
          sendTimeout: sendTimeout,
          receiveTimeout: receiveTimeout,
          headers: {'Content-Type': 'application/json'},
          queryParameters: {'key': apiKey},
        ),
      );

      final response = await dio.get('/models');
      final data = response.data as Map<String, dynamic>;
      final models = (data['models'] as List).map((model) => model['name'] as String).toList();
      for (var i = 0; i < models.length; i++) {
        if (models[i].startsWith('models/')) {
          models[i] = models[i].substring(7);
        }
      }
      return models;
    } catch (e) {
      // 如果獲取模型列表失敗，返回預設模型
      return ['gemini-1.5-pro', 'gemini-1.5-flash', 'gemini-pro'];
    }
  }

  @override
  String roleCast(LlmRole role) => switch (role) {
        LlmRole.system => 'user',
        LlmRole.user => 'user',
        LlmRole.assistant => 'model',
      };

  @override
  List<Map<String, dynamic>> formatMessages(List<LlmMessage> messages) {
    final result = messages
        .map((msg) => {
              'role': roleCast(msg.role),
              'parts': [
                {'text': msg.content}
              ]
            })
        .toList();
    return result;
  }

  @override
  Future<String?> futureText({
    required String apiKey,
    required String modelName,
    required List<LlmMessage> messages,
  }) async {
    try {
      final dio = Dio(
        BaseOptions(
          baseUrl: _baseUrl,
          sendTimeout: sendTimeout,
          receiveTimeout: receiveTimeout,
          headers: {'Content-Type': 'application/json'},
          queryParameters: {'key': apiKey},
        ),
      );
      // 使用 OpenAI 相容的 API 端點
      final response = await dio.post(
        '/models/$modelName:generateContent',
        data: {
          'contents': formatMessages(messages),
          'generationConfig': {'temperature': 0.7},
        },
      );

      final result = response.data['candidates'][0]['content']['parts'][0]['text'];
      return result ?? '';
    } catch (e) {
      return handleError(e);
    }
  }

  @override
  Stream<String?> streamText({
    required String apiKey,
    required String modelName,
    required List<LlmMessage> messages,
  }) async* {
    try {
      final dio = Dio(
        BaseOptions(
          baseUrl: _baseUrl,
          sendTimeout: sendTimeout,
          receiveTimeout: receiveTimeout,
          headers: {'Content-Type': 'application/json'},
          queryParameters: {'key': apiKey},
        ),
      );
      final response = await dio.post<ResponseBody>(
        '/models/$modelName:streamGenerateContent',
        data: {
          'contents': formatMessages(messages),
          'generationConfig': {'temperature': 0.7},
        },
        options: Options(
          responseType: ResponseType.stream, // 關鍵：將響應類型設定為串流
        ),
      );
      await for (final data in response.data!.stream) {
        final chunk = utf8.decode(data); // 將 byte 數據解碼為字串
        // Gemini API 返回的每個事件都是一個完整的 JSON 對象，後跟一個換行符。
        // 然而，一個 TCP 塊可能包含多個或部分的 JSON 對象。
        // 最穩健的方式是緩衝並逐行解析。

        // 通常 Gemini 的串流格式是每行一個 JSON 對象
        // 範例:
        // {"candidates":[{"content":{"parts":[{"text":"你好"}]}}]}
        // {"candidates":[{"content":{"parts":[{"text":"嗎？"}]}}]}

        // 這裡我們直接假設每個 'data' chunk 都是可解析的 JSON 片段，
        // 但實際情況下，一個 Dio 'data' chunk 可能包含多個事件，
        // 或者一個事件被分割在多個 Dio 'data' chunk 中。
        // 因此，需要一個更複雜的解析器，類似 SSE 的事件解析器。

        // 簡單的處理方式：嘗試解析每一個 chunk
        // 由於 Gemini API 通常在每個事件後提供換行符，我們嘗試按行分割。
        // 但一個 'data' chunk 可能包含多個完整的 JSON 對象或部分 JSON。
        // 最安全的方式是累積 buffer 並在遇到換行符時解析。
        // 為簡化起見，這裡直接嘗試解析收到的塊。
        // 實際應用中，如果遇到解析錯誤，需要更精細的緩衝和分割邏輯。

        // 為了處理 Gemini 串流響應的特殊性 (可能是多個 JSON 物件在一個 chunk，
        // 或是部分 JSON 物件，通常以換行符分隔)，我們需要更 robust 的解析。
        // 簡單的做法是假設每個 'data' chunk 已經是完整的 JSON 字符串，
        // 或者至少是能被逐步解析的。
        // Gemini API 的 streaming responses 的格式通常是 newline-delimited JSON.

        final lines = chunk.split('\n'); // 嘗試按行分割
        for (final line in lines) {
          if (line.trim().isEmpty) continue;
          try {
            final jsonResponse = jsonDecode(line);
            if (jsonResponse is Map<String, dynamic> && jsonResponse.containsKey('candidates')) {
              final candidates = jsonResponse['candidates'] as List;
              if (candidates.isNotEmpty) {
                final content = candidates[0]['content'];
                if (content != null && content.containsKey('parts')) {
                  final parts = content['parts'] as List;
                  if (parts.isNotEmpty) {
                    final text = parts[0]['text'] as String?;
                    if (text != null && text.isNotEmpty) {
                      yield text; // 每次產生一個文字片段
                    }
                  }
                }
              }
            }
          } catch (e) {
            // 如果解析失敗，這可能表示這個 chunk 只是部分 JSON
            // 在生產環境中，您可能需要一個更複雜的緩衝機制來處理這種情況。
            // 例如，將不完整的行加入一個緩衝區，等待下一個 chunk 來補齊。
            // 忽略解析錯誤，繼續處理下一個 chunk
          }
        }
      }
    } catch (e) {
      throw handleError(e);
    }
  }

  /// 覆寫錯誤處理方法，處理 Gemini 特定的錯誤
  @override
  String handleError(dynamic error) {
    if (error is DioException) {
      // 處理 HTTP 響應錯誤
      if (error.response != null) {
        final statusCode = error.response!.statusCode;
        final responseData = error.response!.data;

        // 嘗試解析 Gemini API 錯誤響應
        if (responseData is Map<String, dynamic> && responseData.containsKey('error')) {
          final errorInfo = responseData['error'] as Map<String, dynamic>;
          final errorCode = errorInfo['code'] as int?;
          final errorMessage = errorInfo['message'] as String?;
          final errorStatus = errorInfo['status'] as String?;

          // 處理特定的 Gemini 錯誤
          switch (errorCode) {
            case 503:
              if (errorStatus == 'UNAVAILABLE') {
                return '模型目前過載，請稍後再試。建議等待 1-2 分鐘後重新嘗試。';
              }
              break;
            case 429:
              return 'API 請求頻率過高，請稍後再試。';
            case 400:
              return 'API 請求格式錯誤：${errorMessage ?? '未知錯誤'}';
            case 401:
              return 'API 金鑰無效或已過期，請檢查您的 API 金鑰。';
            case 403:
              return 'API 金鑰權限不足或配額已用完。';
            case 404:
              return '指定的模型不存在或不可用。';
            default:
              if (errorMessage != null) {
                return 'Gemini API 錯誤：$errorMessage';
              }
          }
        }

        // 處理一般 HTTP 狀態碼錯誤
        switch (statusCode) {
          case 503:
            return '服務暫時不可用，請稍後再試。';
          case 500:
            return 'Gemini 服務器內部錯誤，請稍後再試。';
          case 502:
          case 504:
            return 'Gemini 服務器連接錯誤，請稍後再試。';
          default:
            return '服務器響應錯誤：HTTP $statusCode';
        }
      }

      // 處理網絡連接錯誤
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
          return '連接 Gemini 服務超時，請檢查網絡連接。';
        case DioExceptionType.sendTimeout:
          return '發送請求到 Gemini 服務超時。';
        case DioExceptionType.receiveTimeout:
          return '接收 Gemini 響應超時，模型可能正在處理複雜請求。';
        case DioExceptionType.connectionError:
          return '無法連接到 Gemini 服務，請檢查網絡連接。';
        default:
          return '網絡錯誤：${error.message}';
      }
    }

    // 處理其他類型的錯誤
    return '未知錯誤：$error';
  }
}
