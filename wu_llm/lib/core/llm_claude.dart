import 'dart:convert';
import 'dart:developer';
import 'package:dio/dio.dart';
import 'llm_message.dart';
import 'llm_base.dart';

/// Claude 服務實現
class LlmClaude extends LlmBase {
  static const String _baseUrl = 'https://api.anthropic.com/v1';

  LlmClaude()
      : super(
          serviceName: 'Claude',
          recommandModels: ['claude-3-5-haiku-********'],
          apiKeyUrl: 'https://console.anthropic.com/account/keys',
        );

  @override
  Future<List<String>> getModels(String? apiKey) async {
    if (apiKey == null) return recommandModels;
    try {
      final dio = Dio(
        BaseOptions(
          baseUrl: _baseUrl,
          sendTimeout: sendTimeout,
          receiveTimeout: receiveTimeout,
          headers: {'Content-Type': 'application/json', 'x-api-key': apiKey, 'anthropic-version': '2023-06-01'},
        ),
      );

      final response = await dio.get('/models');

      final data = response.data as Map<String, dynamic>;
      final models = (data['data'] as List).map((model) => model['id'] as String).toList();
      return models;
    } catch (e) {
      return ['claude-3-7-sonnet-********'];
    }
  }

  @override
  String roleCast(LlmRole role) => switch (role) {
        LlmRole.system => 'user',
        LlmRole.assistant => 'assistant',
        LlmRole.user => 'user',
      };

  @override
  Future<String?> futureText({
    required String apiKey,
    required String modelName,
    required List<LlmMessage> messages,
  }) async {
    try {
      final dio = Dio(
        BaseOptions(
          baseUrl: _baseUrl,
          sendTimeout: sendTimeout,
          receiveTimeout: receiveTimeout,
          headers: {'Content-Type': 'application/json', 'x-api-key': apiKey, 'anthropic-version': '2023-06-01'},
        ),
      );

      // Separate system message
      final systemMessage = messages.firstWhere(
        (msg) => msg.role == LlmRole.system,
        orElse: () => LlmMessage(role: LlmRole.system, content: ''),
      );
      final userMessages = messages.where((msg) => msg.role != LlmRole.system).toList();

      // Prepare request data
      final requestData = {
        'model': modelName,
        'messages': formatMessages(userMessages),
        'max_tokens': 4096,
      };

      // Add system message if present
      if (systemMessage.content.isNotEmpty) {
        requestData['system'] = systemMessage.content;
      }

      final response = await dio.post('/messages', data: requestData);
      final result = response.data['content'][0]['text'];
      return result ?? '';
    } catch (e) {
      return handleError(e);
    }
  }

  @override
  Stream<String?> streamText({
    required String apiKey,
    required String modelName,
    required List<LlmMessage> messages,
  }) async* {
    try {
      final dio = Dio(
        BaseOptions(
          baseUrl: _baseUrl,
          sendTimeout: sendTimeout,
          receiveTimeout: receiveTimeout,
          headers: {'Content-Type': 'application/json', 'x-api-key': apiKey, 'anthropic-version': '2023-06-01'},
        ),
      );

      // Separate system message
      final systemMessage = messages.firstWhere(
        (msg) => msg.role == LlmRole.system,
        orElse: () => LlmMessage(role: LlmRole.system, content: ''),
      );
      final userMessages = messages.where((msg) => msg.role != LlmRole.system).toList();

      // Prepare request data
      final requestData = {
        'model': modelName,
        'messages': formatMessages(userMessages), // Only user/assistant messages
        'stream': true,
        'max_tokens': 4096,
      };

      // Add system message if present
      if (systemMessage.content.isNotEmpty) {
        requestData['system'] = systemMessage.content;
      }

      log("modelName: $modelName");
      final response = await dio.post(
        '/messages',
        data: requestData, // Use updated requestData
        options: Options(responseType: ResponseType.stream),
      );

      final stream = response.data.stream as Stream<List<int>>;
      String buffer = '';

      await for (final chunk in stream) {
        buffer += utf8.decode(chunk);
        final lines = buffer.split('\n');
        buffer = lines.last;

        for (final line in lines) {
          if (line.startsWith('data: ')) {
            final data = line.substring(6);
            if (data == '[DONE]') continue;
            try {
              final json = jsonDecode(data);
              final content = json['delta']['text'] as String?;
              if (content != null) yield content;
            } catch (e) {
              // 忽略解析錯誤
            }
          }
        }
      }
    } catch (e) {
      yield handleError(e);
    }
  }
}
