# 產品需求文件 (PRD) - Advanced Tarot Weaver 套件庫

## 1. 產品概述

### 1.1 專案簡介
Advanced Tarot Weaver 是一個基於 Flutter/Dart 的模組化套件庫，專為開發中華傳統命理與現代 AI 技術結合的應用程式而設計。該專案包含四個核心套件，提供從基礎 UI 元件到複雜命理演算法的完整解決方案。

### 1.2 目標用戶
- **主要用戶**：Flutter 開發者，特別是開發命理、占卜、易學相關應用的開發團隊
- **次要用戶**：對中華傳統文化與現代技術結合感興趣的研究者和愛好者
- **終端用戶**：使用基於此套件開發的命理應用程式的一般用戶

### 1.3 核心價值主張
- **模組化設計**：四個獨立套件可單獨使用或組合使用
- **傳統與現代結合**：將古老的命理智慧與現代 AI 技術完美融合
- **響應式設計**：支援手機、平板、桌面多平台的自適應佈局
- **商業化支援**：內建廣告管理和變現機制

## 2. 套件架構

### 2.1 wu_core - 核心基礎套件
**功能定位**：提供基礎 UI 元件和響應式佈局系統

#### 2.1.1 響應式佈局系統
- **SizeLevel 枚舉**：定義三種螢幕尺寸等級
  - S (450px)：手機版，邊距 16px，間距 12px
  - M (800px)：平板版，邊距 24px，間距 12px  
  - L (1440px)：桌面版，邊距 32px，間距 16px
- **AutoFrame 元件**：自適應框架容器，自動選擇合適的尺寸等級
- **AutoLayout 元件**：根據螢幕寬度自動選擇最適合的佈局版本

#### 2.1.2 UI 元件庫
- **DivinTile**：可滑動的占卜項目元件，支援刪除和複製操作
- **CardDrewer**：翻牌動畫元件，支援自定義翻牌方向和速度
- **CapsuleText**：膠囊狀文字元件，左右分色設計
- **InfoRow**：資訊行元件，用於顯示鍵值對資訊
- **WheelPicker**：滾輪選擇器元件

#### 2.1.3 擴展功能
- **DateTime 擴展**：提供日期格式化、干支時辰計算等功能
- **String 擴展**：字串反轉、排序、子字串操作
- **List 擴展**：列表切換、存在性檢查等實用方法
- **ChineseNumber**：中文數字轉換工具，支援大小寫

### 2.2 wu_fortune - 命理核心套件
**功能定位**：提供完整的中華傳統命理演算法和資料結構

#### 2.2.1 基礎命理系統
- **天干地支系統**：
  - Gan 類：十天干的完整實作，包含五行屬性、陰陽屬性
  - Zhi 類：十二地支的完整實作，包含時辰、生肖對應
  - GanZhi 類：干支組合，提供旬首、旬空、符頭計算
- **五行系統**：
  - Wuxing 類：五行屬性管理，包含顏色對應
  - ShengKe 類：五行生剋關係計算
  - LifeState 類：長生十二神狀態計算

#### 2.2.2 曆法系統
- **干支曆轉換**：太陽曆與干支曆的精確轉換
- **農曆系統**：農曆日期計算和顯示
- **藏曆系統**：藏族曆法支援
- **節氣系統**：二十四節氣的精確計算
- **道教齋日**：道教重要齋戒日期計算

#### 2.2.3 易經系統
- **八卦系統**：
  - Gua8 類：八卦的完整實作，包含納甲、方位、五行屬性
  - 支援先天八卦和後天八卦的轉換
- **六十四卦系統**：
  - Gua64 類：六十四卦的完整實作
  - 包含卦名、卦象、大衍之數等屬性
- **易經占卜**：
  - Yijing 類：支援梅花易數、六爻占卜
  - 本卦、變卦、互卦、錯卦、綜卦的自動計算
  - 動爻、靜爻的識別和處理

#### 2.2.4 奇門遁甲系統
- **奇門盤構建**：
  - QimenDesk 類：奇門遁甲盤面的完整構建
  - 支援時家奇門的標準排盤方法
- **奇門要素**：
  - 九星、八門、八神的完整實作
  - 天盤、地盤、人盤的三才配置
- **奇門分析**：
  - 用神定位和分析
  - 格局判斷和吉凶評估

#### 2.2.5 擇日系統
- **建除十二星**：黃道吉日的計算
- **二十八宿**：星宿值日的計算
- **神煞系統**：各種神煞的計算和應用
- **最佳日期推薦**：根據用途推薦最適合的日期

### 2.3 wu_llm - AI 語言模型套件
**功能定位**：提供多平台 LLM 服務的統一介面

#### 2.3.1 支援的 LLM 服務
- **OpenAI ChatGPT**：GPT-3.5、GPT-4 系列模型
- **Google Gemini**：Gemini Pro、Gemini Flash 系列
- **Anthropic Claude**：Claude 3 系列模型
- **其他服務**：
  - Groq：高速推理服務
  - DeepSeek：中文優化模型
  - Mistral：歐洲開源模型
  - Perplexity：搜索增強生成
  - Ollama：本地部署方案

#### 2.3.2 統一 API 介面
- **LlmBase 抽象類**：定義統一的服務介面
- **LlmMessage 類**：標準化的訊息格式
- **服務工廠模式**：通過服務名稱動態創建服務實例

#### 2.3.3 功能特性
- **流式和非流式**：支援兩種文本生成模式
- **模型管理**：動態獲取可用模型列表
- **錯誤處理**：統一的錯誤處理機制
- **超時控制**：可配置的發送和接收超時

### 2.4 wu_admob - 廣告變現套件
**功能定位**：提供完整的移動廣告管理和變現解決方案

#### 2.4.1 廣告類型支援
- **橫幅廣告**：
  - BannerController：橫幅廣告的生命週期管理
  - 自動載入和錯誤重試機制
  - 跨平台適配（iOS/Android）
- **插頁廣告**：
  - InterstitialController：插頁廣告的定時顯示
  - 可配置的顯示間隔
- **獎勵廣告**：
  - RewardController：獎勵廣告的完整管理
  - 虛擬貨幣系統整合

#### 2.4.2 虛擬貨幣系統
- **AI 幣管理**：用戶虛擬貨幣的增減操作
- **持久化存儲**：使用 GetStorage 進行本地存儲
- **獎勵機制**：觀看廣告獲得虛擬貨幣

#### 2.4.3 應用商店評價
- **StoreReview 類**：智能評價提醒系統
- **條件觸發**：基於使用天數和消費金額的觸發機制
- **跨平台支援**：iOS App Store 和 Google Play 的原生評價

#### 2.4.4 UI 元件
- **ChargeTile**：充值介面元件
- **ReadingButton**：消費型按鈕元件，整合虛擬貨幣扣除

## 3. 技術架構

### 3.1 開發環境
- **Flutter SDK**：3.1.3+
- **Dart SDK**：3.0.6+
- **狀態管理**：GetX 4.7.2
- **本地存儲**：GetStorage 2.1.1、Hive CE 2.11.3
- **網路請求**：Dio（用於 LLM 服務）

### 3.2 依賴關係
```
wu_core (基礎套件)
├── wu_fortune (依賴 wu_core)
├── wu_llm (獨立套件)
└── wu_admob (依賴 wu_core)
```

### 3.3 設計模式
- **工廠模式**：LLM 服務的動態創建
- **控制器模式**：廣告管理的狀態控制
- **擴展模式**：基礎類型的功能擴展
- **組合模式**：UI 元件的模組化組合

## 4. 核心功能需求

### 4.1 響應式佈局需求
- **FR-001**：系統必須支援手機、平板、桌面三種螢幕尺寸的自適應佈局
- **FR-002**：佈局系統必須提供統一的間距和邊距規範
- **FR-003**：元件必須能夠根據螢幕尺寸自動調整顯示效果

### 4.2 命理計算需求
- **FR-004**：系統必須提供精確的干支曆轉換功能
- **FR-005**：系統必須支援完整的易經六十四卦計算
- **FR-006**：系統必須提供奇門遁甲的標準排盤功能
- **FR-007**：系統必須支援多種擇日方法的計算

### 4.3 AI 整合需求
- **FR-008**：系統必須支援至少 5 種主流 LLM 服務
- **FR-009**：系統必須提供統一的 API 介面
- **FR-010**：系統必須支援流式和非流式兩種生成模式

### 4.4 商業化需求
- **FR-011**：系統必須支援橫幅、插頁、獎勵三種廣告類型
- **FR-012**：系統必須提供虛擬貨幣管理功能
- **FR-013**：系統必須支援應用商店評價提醒

## 5. 非功能性需求

### 5.1 性能需求
- **NFR-001**：命理計算響應時間不超過 100ms
- **NFR-002**：LLM 服務連接超時不超過 30 秒
- **NFR-003**：UI 動畫幀率保持在 60fps

### 5.2 可用性需求
- **NFR-004**：API 介面必須簡潔易用
- **NFR-005**：錯誤訊息必須清晰明確
- **NFR-006**：文檔覆蓋率達到 80% 以上

### 5.3 相容性需求
- **NFR-007**：支援 iOS 12+ 和 Android API 21+
- **NFR-008**：支援 Windows、macOS、Linux 桌面平台
- **NFR-009**：支援 Web 平台（部分功能）

## 6. 開發里程碑

### 6.1 第一階段：核心基礎（已完成）
- wu_core 套件的響應式佈局系統
- 基礎 UI 元件庫
- 擴展功能實作

### 6.2 第二階段：命理核心（已完成）
- wu_fortune 套件的命理演算法
- 易經、奇門遁甲系統
- 擇日和曆法系統

### 6.3 第三階段：AI 整合（已完成）
- wu_llm 套件的多平台 LLM 支援
- 統一 API 介面
- 流式處理機制

### 6.4 第四階段：商業化（已完成）
- wu_admob 套件的廣告管理
- 虛擬貨幣系統
- 應用商店評價機制

## 7. 風險評估

### 7.1 技術風險
- **LLM 服務穩定性**：依賴第三方服務，可能面臨服務中斷
- **命理演算法準確性**：需要持續驗證和優化
- **跨平台相容性**：不同平台可能存在差異

### 7.2 商業風險
- **廣告政策變更**：廣告平台政策可能影響變現
- **用戶接受度**：傳統命理與現代技術的結合需要市場驗證

### 7.3 法律風險
- **隱私保護**：需要符合各地區的隱私保護法規
- **內容合規**：命理內容需要符合相關法規要求

## 8. 後續發展規劃

### 8.1 功能擴展
- 增加更多命理系統（如紫微斗數、八字詳批）
- 支援更多 LLM 服務提供商
- 增強 AI 分析能力

### 8.2 技術優化
- 性能優化和記憶體管理
- 離線功能支援
- 更好的錯誤處理機制

### 8.3 生態建設
- 開發者文檔完善
- 示例應用程式
- 社群支援和反饋機制
