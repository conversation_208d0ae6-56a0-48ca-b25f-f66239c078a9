# 產品需求文件 (PRD) - AI Tarot Master 應用程式

## 1. 產品概述

### 1.1 產品簡介
**AI Tarot Master** 是一款融合人工智慧與神秘塔羅學的占卜應用程式，支援多國語言，讓全球使用者都能輕鬆體驗專業塔羅解讀。該應用基於 Advanced Tarot Weaver 套件庫開發，提供完整的塔羅占卜體驗，從卡牌抽取到 AI 智能解讀。

### 1.2 目標用戶
- **主要用戶**：塔羅愛好者、占卜初學者、尋求人生指引的用戶
- **次要用戶**：對神秘學感興趣的探索者、決策困難者
- **地理分佈**：全球用戶，支援多語言（中文、英文、日文、韓文、西班牙文、法文）

### 1.3 核心價值主張
- **AI 智能解讀**：結合多種 LLM 服務，提供專業且個性化的塔羅解讀
- **多元占卜模式**：支援四種不同的占卜頻道，滿足各種需求
- **直觀操作體驗**：三種抽牌模式，從翻牌到隨機選擇
- **跨平台支援**：手機、平板、桌面全平台適配

## 2. 產品架構

### 2.1 技術架構
- **前端框架**：Flutter 3.1.3+
- **狀態管理**：GetX 4.7.2
- **本地存儲**：Hive CE 2.11.3 + GetStorage 2.1.1
- **AI 服務**：wu_llm 套件（支援 Gemini、ChatGPT、Groq 等）
- **UI 框架**：wu_core 響應式佈局系統
- **廣告變現**：wu_admob 廣告管理套件

### 2.2 應用架構
```
HomePage (主頁)
├── ChannelScr (頻道選擇)
├── SettingPage (設定頁面)
└── DocListPage (文檔頁面)

頻道系統
├── Divine (占卜頻道) - 5張牌
├── Journal (日誌頻道) - 4張牌  
├── Rating (評分頻道) - 可變張數
└── Decision (決策頻道) - 1張牌
```

## 3. 核心功能需求

### 3.1 頻道系統 (Channel System)

#### 3.1.1 Divine 占卜頻道
- **功能描述**：使用五張塔羅牌進行深度占卜，識別關鍵牌並揭示命運奧秘
- **適用場景**：愛情、事業、人生重大決策
- **牌陣結構**：前綴牌（當前情境）+ 關鍵牌（核心結果）+ 後綴牌（後續影響）
- **AI 解讀**：提供詳細的牌陣分析、摘要和深度解讀

#### 3.1.2 Journal 日誌頻道  
- **功能描述**：四張牌的每日塔羅日誌，了解當日能量流動
- **適用場景**：每日運勢、日常指引、個人成長追蹤
- **特色功能**：建立個人檔案，記錄每日牌卡，觀察命運走向

#### 3.1.3 Rating 評分頻道
- **功能描述**：為多個選項各抽一張牌，提供評分和比較分析
- **適用場景**：多選題決策、選擇困難、方案比較
- **評分機制**：0-10分評分系統，根據牌義評估各選項優勢
- **輸出格式**：JSON 格式，包含選項名稱、評分、解讀

#### 3.1.4 Decision 決策頻道
- **功能描述**：一張牌回答 YES/NO 問題，提供直接明確的建議
- **適用場景**：快速決策、是非判斷、簡單選擇
- **輸出格式**：明確的 YES/NO 答案 + 詳細建議

### 3.2 抽牌系統 (Card Drawing System)

#### 3.2.1 FlipCard 翻牌模式
- **功能描述**：模擬真實翻牌體驗，點擊翻轉卡牌
- **技術實現**：使用 FlipCard 元件，支援水平翻轉動畫
- **用戶體驗**：直觀的觸控操作，增強占卜儀式感

#### 3.2.2 Random 隨機模式
- **功能描述**：系統隨機選擇卡牌，快速完成抽牌
- **技術實現**：基於時間種子的隨機演算法
- **適用場景**：快速占卜、遠程占卜

#### 3.2.3 Choice 選擇模式
- **功能描述**：用戶主動選擇喜歡的卡牌
- **分類系統**：支援大阿卡那、小阿卡那分類瀏覽
- **用戶體驗**：提供更多控制權和參與感

### 3.3 AI 解讀系統

#### 3.3.1 多 LLM 支援
- **支援服務**：Gemini、ChatGPT、Groq
- **模型選擇**：用戶可自由切換不同 AI 模型
- **API 管理**：支援多個 API Key 配置

#### 3.3.2 智能提示詞系統
- **問題強化**：AI 自動生成五個更具體的問題選項
- **後續提問**：根據解讀結果提供深入探索的問題
- **領域專家**：AI 根據問題領域扮演相應專家角色

#### 3.3.3 解讀格式化
- **Markdown 支援**：解讀結果支援 Markdown 格式顯示
- **結構化輸出**：摘要、詳細解讀、建議分離
- **牌種統計**：自動分析大阿卡那、四花色、宮廷牌、逆位牌數量

### 3.4 數據管理系統

#### 3.4.1 本地存儲
- **Hive 數據庫**：使用 Hive CE 進行高效本地存儲
- **模型設計**：TarotModel 包含完整占卜記錄
- **數據持久化**：自動保存占卜歷史和用戶設定

#### 3.4.2 數據結構
```dart
class TarotModel {
  DateTime createAt, updateAt;
  ChannelEnum channel;
  String question;
  String? options, reading, summary, comment, yesno;
  DrewModeEnum drewMode;
  List<CardDrew> cards;
}
```

### 3.5 用戶介面系統

#### 3.5.1 響應式設計
- **多設備適配**：手機、平板、桌面自適應佈局
- **斷點設計**：450px (手機)、800px (平板)、1440px (桌面)
- **動態佈局**：根據螢幕尺寸調整頻道卡片排列

#### 3.5.2 主題系統
- **配色方案**：FlexScheme.purpleBrown 紫棕色主題
- **深色模式**：支援系統主題自動切換
- **Material Design**：遵循 Material Design 設計規範

#### 3.5.3 國際化支援
- **多語言**：中文、英文、日文、韓文、西班牙文、法文
- **本地化**：根據設備語言自動選擇
- **文檔系統**：每種語言提供完整的使用說明

## 4. 商業化功能

### 4.1 虛擬貨幣系統
- **AI 幣機制**：用戶通過觀看廣告獲得虛擬貨幣
- **消費模式**：AI 解讀需要消耗 AI 幣
- **初始額度**：新用戶獲得 10 個 AI 幣

### 4.2 廣告系統
- **獎勵廣告**：觀看廣告獲得 AI 幣
- **廣告平台**：Google AdMob
- **頻率控制**：30 秒間隔的廣告顯示

### 4.3 應用評價
- **智能提醒**：基於使用天數和消費金額觸發
- **原生評價**：iOS App Store 和 Google Play 原生評價

## 5. 技術特色

### 5.1 塔羅牌系統
- **完整牌組**：78 張韋特塔羅牌
- **逆位支援**：支援正位和逆位解讀
- **牌義數據**：內建完整的牌義資料庫

### 5.2 性能優化
- **圖片管理**：高效的塔羅牌圖片載入
- **狀態管理**：GetX 響應式狀態管理
- **記憶體優化**：合理的資源管理和釋放

### 5.3 跨平台支援
- **移動平台**：iOS 12+、Android API 21+
- **桌面平台**：Windows、macOS、Linux
- **視窗管理**：桌面版支援視窗大小記憶

## 6. 用戶體驗設計

### 6.1 操作流程
1. **選擇頻道** → 2. **輸入問題** → 3. **選擇抽牌模式** → 4. **抽取卡牌** → 5. **AI 解讀** → 6. **查看結果**

### 6.2 視覺設計
- **神秘感**：紫棕色主題營造神秘氛圍
- **直觀性**：清晰的圖標和操作指引
- **沉浸感**：流暢的翻牌動畫和過渡效果

### 6.3 互動設計
- **觸覺反饋**：適當的觸覺反饋增強體驗
- **動畫效果**：流暢的頁面切換和卡牌動畫
- **錯誤處理**：友善的錯誤提示和恢復機制

## 7. 品質保證

### 7.1 功能測試
- **占卜流程**：完整的占卜流程測試
- **AI 整合**：多 LLM 服務的穩定性測試
- **數據持久化**：本地存儲的可靠性測試

### 7.2 性能測試
- **響應時間**：AI 解讀響應時間控制在合理範圍
- **記憶體使用**：監控記憶體使用情況
- **電池消耗**：優化電池使用效率

### 7.3 相容性測試
- **設備相容**：多種設備和螢幕尺寸測試
- **系統相容**：不同作業系統版本測試
- **語言相容**：多語言顯示和功能測試

## 8. 發布策略

### 8.1 平台發布
- **Google Play Store**：Android 版本發布
- **Apple App Store**：iOS 版本發布
- **Microsoft Store**：Windows 版本發布
- **GitHub Releases**：開源版本發布

### 8.2 版本管理
- **語義化版本**：遵循語義化版本規範
- **漸進式發布**：分階段推出新功能
- **熱更新支援**：Shorebird 代碼推送

### 8.3 用戶反饋
- **應用內反饋**：提供反饋收集機制
- **社群支援**：建立用戶社群
- **持續改進**：基於用戶反饋持續優化

## 9. 風險評估與應對

### 9.1 技術風險
- **AI 服務依賴**：多 LLM 服務備援機制
- **API 限制**：合理的 API 使用頻率控制
- **數據安全**：本地存儲確保用戶隱私

### 9.2 商業風險
- **廣告政策**：遵循廣告平台政策
- **內容合規**：確保占卜內容符合各地法規
- **用戶接受度**：持續優化用戶體驗

### 9.3 法律風險
- **隱私保護**：符合 GDPR、CCPA 等隱私法規
- **免責聲明**：明確占卜娛樂性質
- **年齡限制**：適當的年齡使用建議

## 10. 未來發展規劃

### 10.1 功能擴展
- **更多牌組**：支援其他塔羅牌系統
- **社交功能**：用戶間分享和交流
- **專家模式**：更深度的占卜分析

### 10.2 技術升級
- **AI 能力**：整合更先進的 AI 模型
- **AR/VR**：探索沉浸式占卜體驗
- **語音交互**：語音問答和解讀

### 10.3 市場拓展
- **地區本地化**：針對不同地區的文化適配
- **合作夥伴**：與占卜師和靈性導師合作
- **教育內容**：提供塔羅學習課程
