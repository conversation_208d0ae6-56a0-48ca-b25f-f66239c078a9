name: adv_tarot
description: >-
  AI塔羅大師是一款AI塔羅占卜應用，支援自由提問與智能解讀，提供決策指引與運勢分析，隨時為你解惑！

publish_to: 'none'

version: 1.2.1+23

environment:
  sdk: ^3.6.0

# flutter pub upgrade --tighten
dependencies:
  flutter:
    sdk: flutter

  # database
  path_provider: ^2.1.5
  hive_ce: ^2.11.3
  hive_ce_flutter: ^2.3.1

  cupertino_icons: ^1.0.8
  intl: ^0.20.2
  get: ^4.7.2
  get_storage: ^2.1.1
  flutter_markdown: ^0.7.7+1
  flutter_form_builder: ^10.0.1
  form_builder_validators: ^11.1.2
  grouped_list: ^6.0.0
  flex_color_scheme: ^8.2.0

  flutter_svg: ^2.1.0
  url_launcher: ^6.3.1
  screenshot: ^3.0.0
  # image: ^4.5.2
  share_plus: ^11.0.0
  window_manager: ^0.5.0

  upgrader: ^11.4.0
  shorebird_code_push: ^2.0.4

  wu_core:
    path: ../adv_package/wu_core
  wu_llm:
    path: ../adv_package/wu_llm
  wu_admob:
    path: ../adv_package/wu_admob 

dependency_overrides:
  intl: ^0.20.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^6.0.0
  build_runner: ^2.4.15
  # dart run build_runner build
  # flutter packages pub run build_runner build

  hive_ce_generator: ^1.9.2

  flutter_launcher_icons: ^0.14.4
  # dart run flutter_launcher_icons

  flutter_native_splash: ^2.4.6
  # flutter pub run flutter_native_splash:create

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/adv_tarot.png"
  image_path_android: "assets/adv_tarot.png"
  remove_alpha_ios: true
  min_sdk_android: 21 # android min sdk min:16, default 21
  web:
    generate: true
    background_color: "#hexcode"
    theme_color: "#hexcode"
  windows:
    generate: true
    icon_size: 48 # min:48, max:256, default: 48
  macos:
    generate: true
    minimum_system_version: "10.13"

flutter_native_splash:
  android: true
  ios: true
  color: "#C9BC9C"
  image: assets/adv_tarot.png
  color_dark: "#1a1a1a"
  fullscreen: true

flutter:
  uses-material-design: true
  assets:
    - assets/
    - assets/images/
    - assets/images/channels/
    - assets/images/icons/
    - assets/images/waite/
    - assets/docs/
    - assets/docs/en/
    - assets/docs/ja/
    - assets/docs/ko/
    - assets/docs/zh/
    - assets/jsons/
    - shorebird.yaml
