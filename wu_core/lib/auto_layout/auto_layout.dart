import 'package:flutter/material.dart';
import 'auto_frame.dart';

export 'auto_frame.dart' show SizeLevel;

typedef ContentBuilder = Widget Function(BuildContext context, SizeLevel sizeLevel);

/// 響應式佈局元件
class AutoLayout extends StatelessWidget {
  final ContentBuilder buildContentS;
  final ContentBuilder? buildContentL;
  final ContentBuilder? buildContentM;

  const AutoLayout({
    super.key,
    required this.buildContentS,
    this.buildContentM,
    this.buildContentL,
  });

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final sizeLevel = width < 600
        ? SizeLevel.S
        : width < 1200
            ? SizeLevel.M
            : SizeLevel.L;

    return switch (sizeLevel) {
      SizeLevel.S => buildContentS(context, sizeLevel),
      SizeLevel.M => buildContentM?.call(context, sizeLevel) ?? buildContentS(context, sizeLevel),
      SizeLevel.L => buildContentL?.call(context, sizeLevel) ??
          buildContentM?.call(context, sizeLevel) ??
          buildContentS(context, sizeLevel),
    };
  }
}
