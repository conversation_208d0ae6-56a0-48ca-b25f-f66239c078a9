import 'package:flutter/material.dart';

class RailScaffold extends StatelessWidget {
  final NavigationRail navigationRail;
  final Widget body;

  const RailScaffold({
    super.key,
    required this.navigationRail,
    required this.body,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(width: 80, child: navigationRail),
          Expanded(child: body),
        ],
      ),
    );
  }
}
