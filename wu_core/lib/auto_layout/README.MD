# Auto Layout

這個資料夾包含了用於響應式佈局的元件。

## SizeLevel

定義了不同螢幕尺寸等級的斷點、邊距和間距。

系統會根據不同螢幕寬度自動調整邊距和間距：
- S (breakpoint: 450, gutter: 12, margin: 16): 手機版（大螢幕手機）
- M (breakpoint: 800, gutter: 12, margin: 24): 平板版（直向）
- L (breakpoint: 1440, gutter: 16, margin: 32): 電腦版（桌面）

## FrameBuilder

框架建構器函數的類型定義。

參數說明：
- context: 建構上下文
- sizeLevel: 當前螢幕尺寸等級
- frameWidth: 框架實際可用寬度，理論上用不到

## AutoFrame

響應式框架元件。

此元件提供一個自適應的佈局容器，能夠根據不同螢幕尺寸自動調整內容區域的寬度和間距。

主要特點：
1. 自動響應式佈局：依據螢幕寬度自動選擇合適的尺寸等級
2. 統一的間距系統：提供一致的邊距和間距設定
3. 內容寬度限制：確保在大螢幕上內容不會過寬
4. 可選的捲動支援：支援單向捲動 (Note: isScrollable is not a parameter of AutoFrame based on the code. This example might be outdated or incorrect.)

尺寸等級設定：
- S (450px)：適用於大螢幕手機
- M (800px)：適用於平板直式
- L (1440px)：適用於桌面電腦

使用範例：
```dart
AutoFrame(
  backgroundColor: Colors.white,
  alignment: Alignment.center,
  builder: (context, sizeLevel, frameWidth) {
    return Column(
      children: [
        // 您的內容
      ],
    );
  },
)
```

注意事項：
- 此元件會自動處理 SafeArea，確保內容不會被系統 UI 遮擋
- 內部佈局建議使用 Flex 或 Column/Row 的 spacing 屬性來保持一致的間距
- 需要自定義間距時，可使用 SizedBox 進行調整
- 當螢幕寬度超過 L 斷點時，內容寬度會被限制在 L 斷點值

## AutoLayout

響應式佈局元件。

此元件用於根據螢幕寬度自動選擇最適合的佈局版本。
支援三種不同的佈局尺寸：
- L（電腦版）：適用於桌面電腦
- M（平板版）：適用於平板裝置
- S（手機版）：適用於手機裝置

佈局選擇邏輯：
1. 當螢幕寬度大於 L 斷點時，直接使用 L 版佈局 (Note: The code logic is based on fixed breakpoints 600 and 1200, not SizeLevel breakpoints)
2. 其他情況下，根據 Frame 提供的 widthLevel 選擇對應的佈局 (Note: This seems incorrect based on the provided code)
3. 如果指定的佈局不存在，會依序嘗試使用較大尺寸的佈局作為備選 (Note: This seems incorrect based on the provided code)

使用範例：
```dart
AutoLayout(
  buildContentL: (context, sizeLevel) => DesktopLayout(),    // 電腦版佈局
  buildContentS: (context, sizeLevel) => MobileLayout(),     // 手機版佈局
)
```

## MobileLayoutBase

手機版佈局基礎類別。

提供手機版佈局的基本結構，包含 SafeArea 包裝以確保內容不會被系統 UI 遮擋。
子類別需要實作 buildMobileContent 方法來提供具體的佈局內容。

手機版特別考量：
- 建議重新設計操作流程，使其更適合觸控操作
- 多欄佈局建議改用 TabBarView
- 單欄佈局建議改用 ListView

使用範例：
```dart
class MyMobileLayout extends MobileLayoutBase {
  @override
  Widget buildMobileContent(BuildContext context, SizeLevel sizeLevel) {
    return Scaffold(
      // 實作手機版佈局內容
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: SizeLevel.S.margin),
        child: YourWidget(), // 您的元件可以在這裡控制版面呈現，同時保持規範的邊界寬度
      ),
    );
  }
}
```

## CommonLayoutBase

通用版佈局基礎類別。

使用 Frame 元件作為基礎容器，提供以下功能：
1. 自動限制頁面最大寬度，確保在大螢幕上的最佳顯示效果
2. 提供響應式佈局所需的尺寸等級（sizeLevel）和實際寬度（frameWidth）
3. 內建頁面捲動功能 (Note: AutoFrame does not have isScrollable parameter based on the code)

佈局層級說明：
- 第一層（頁面）：可直接使用 sizeLevel 和 frameWidth 進行佈局
- 第二層（多欄式）：需使用 LayoutBuilder 取得實際可用寬度
- 建議使用 SizeLevel.gutter 作為欄位間距參考值

使用範例：
```dart
class MyCommonLayout extends CommonLayoutBase {
  @override
  Widget buildCommonContent(BuildContext context, SizeLevel sizeLevel, double frameWidth) {
    return Column(
      children: [
        // 實作您的通用版佈局內容
        Padding(
          padding: EdgeInsets.symmetric(horizontal: sizeLevel.margin),
          child: YourWidget(), // 您的元件可以在這裡控制版面呈現，同時保持規範的邊界寬度
        ),
      ],
    );
  }
}