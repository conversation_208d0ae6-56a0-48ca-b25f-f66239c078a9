// ignore_for_file: constant_identifier_names

import 'dart:developer' as console;

import 'package:flutter/material.dart';

/// 響應式佈局系統配置
enum SizeLevel {
  S(breakpoint: 450, gutter: 12, margin: 16), // 手機版（大螢幕手機）
  M(breakpoint: 800, gutter: 12, margin: 24), // 平板版（直向）
  L(breakpoint: 1440, gutter: 16, margin: 32); // 電腦版（桌面）

  final double breakpoint;
  final double gutter;
  final double margin;

  const SizeLevel({required this.breakpoint, required this.gutter, required this.margin});
}

/// 框架建構器函數類型定義
typedef FrameBuilder = Widget Function(BuildContext context, SizeLevel sizeLevel, double frameWidth);

/// 響應式框架元件
class AutoFrame extends StatelessWidget {
  final Color? backgroundColor;
  final AlignmentGeometry? alignment;
  final FrameBuilder builder;

  const AutoFrame({super.key, this.backgroundColor, this.alignment, required this.builder});

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final sizeLevel = SizeLevel.values.reversed.firstWhere((e) => width >= e.breakpoint, orElse: () => SizeLevel.S);
    // 如果寬度超過 L 的斷點，則使用 L 的斷點
    final frameWidth = width >= SizeLevel.L.breakpoint ? SizeLevel.L.breakpoint : width;
    console.log('sizeLevel: $sizeLevel, frameWidth: $frameWidth');

    Widget child = Align(
      alignment: Alignment.topCenter,
      child: Container(
        width: frameWidth,
        // 限定可用區域為可視高度，這樣裡面使用 ScrollView 不會出現錯誤
        height: MediaQuery.of(context).size.height,
        color: backgroundColor,
        child: builder(context, sizeLevel, frameWidth),
      ),
    );

    return SafeArea(child: Scaffold(body: child));
  }
}
