import 'package:flutter/material.dart';

class HeaderRow extends StatelessWidget {
  final Widget title;
  final Widget? leftWidget;
  final Widget? rightWidget;
  const HeaderRow({
    super.key,
    required this.title,
    this.leftWidget,
    this.rightWidget,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Expanded(
          child: <PERSON>gn(
            alignment: Alignment.centerLeft,
            child: leftWidget ?? Container(),
          ),
        ),
        title,
        Expanded(
          child: Align(
            alignment: Alignment.centerRight,
            child: rightWidget ?? Container(),
          ),
        ),
      ],
    );
  }
}
