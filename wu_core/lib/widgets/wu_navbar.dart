import 'package:flutter/material.dart';

import '../auto_layout/menu_item.dart';

class WuNavbar extends StatelessWidget {
  final List<MenuItem> menus;
  final int selectedIndex;
  final Function(int)? onDestinationSelected;
  const WuNavbar({
    super.key,
    required this.menus,
    required this.selectedIndex,
    this.onDestinationSelected,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return NavigationBarTheme(
      data: NavigationBarThemeData(
        backgroundColor: colorScheme.primaryContainer,
        indicatorColor: colorScheme.primary,
        labelTextStyle: WidgetStatePropertyAll(
          TextStyle(
            color: colorScheme.onPrimaryContainer,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      child: NavigationBar(
        selectedIndex: selectedIndex,
        onDestinationSelected: onDestinationSelected,
        destinations: List.generate(
          menus.length,
          (index) {
            final item = menus[index];
            return NavigationDestination(
              icon: item.normalIcon,
              label: item.title,
              selectedIcon: IconTheme(
                data: IconThemeData(color: colorScheme.onPrimary),
                child: item.activeIcon,
              ),
            );
          },
        ),
      ),
    );
  }
}
