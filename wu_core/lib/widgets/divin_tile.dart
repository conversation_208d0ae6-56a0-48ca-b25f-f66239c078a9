import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';

class DivinTile extends StatelessWidget {
  final Function() onDelete;
  final Function()? onClone;
  final Function()? onTap;
  final Function()? onLongPress;
  final Widget? leading;
  final Widget title;
  final Widget? subtitle;
  final String deleteText;
  final String dupicateText;
  const DivinTile({
    super.key,
    required this.onDelete,
    this.onClone,
    this.onTap,
    this.onLongPress,
    this.leading,
    required this.title,
    this.subtitle,
    this.deleteText = "Delete",
    this.dupicateText = "Clone",
  });

  @override
  Widget build(BuildContext context) {
    return Slidable(
      key: ValueKey(key),
      startActionPane: ActionPane(
        motion: const ScrollMotion(),
        children: [
          SlidableAction(
            onPressed: (_) => onDelete(),
            backgroundColor: const Color(0xFFFE4A49),
            foregroundColor: Colors.white,
            icon: Icons.delete,
            label: deleteText,
          ),
          if (onClone != null)
            SlidableAction(
              onPressed: (_) => onClone!(),
              backgroundColor: const Color(0xFF21B7CA),
              foregroundColor: Colors.white,
              icon: Icons.copy,
              label: dupicateText,
            ),
        ],
      ),
      child: InkWell(
        onTap: onTap,
        onLongPress: onLongPress,
        child: Card(
          elevation: 3.0,
          child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 8.0,
                vertical: 6.0,
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (leading != null) leading!,
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          DefaultTextStyle(style: Theme.of(context).textTheme.titleMedium!, child: title),
                          if (subtitle != null)
                            DefaultTextStyle(
                                style: Theme.of(context).textTheme.bodyMedium!.copyWith(color: Colors.grey),
                                child: subtitle!),
                        ],
                      ),
                    ),
                  ),
                  const Icon(Icons.chevron_right)
                ],
              )),
        ),
      ),
    );
  }
}
