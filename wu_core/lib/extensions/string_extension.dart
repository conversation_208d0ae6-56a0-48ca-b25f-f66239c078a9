extension StringExtension on String {
  String reverse() {
    return split('').reversed.join("");
  }

  String fateSort() {
    var lst = runes.toSet().toList()..sort();
    return lst.join("");
  }

  String substr(int start, int? end) {
    var ret = substring(start);
    if (end != null && end < 0) ret = substring(0, ret.length + end);
    return ret;
  }

  bool operator >=(String other) {
    return compareTo(other) >= 0;
  }

  bool operator <=(String other) {
    return compareTo(other) <= 0;
  }
}
