part of "../wu_admob.dart";

// Get.find<InterstitialController>().show();

class InterstitialController extends GetxController {
  final String? iosAdId;
  final String? androidAdId;
  final Duration showDuration;

  InterstitialController({
    this.androidAdId,
    this.iosAdId,
    this.showDuration = const Duration(minutes: 3),
  });

  @override
  void onInit() {
    super.onInit();
    Future.delayed(showDuration, () => adLoad());
  }

  String? get unitId {
    try {
      return Platform.isAndroid ? androidAdId : iosAdId;
    } catch (error) {
      return null;
    }
  }

  bool get loaded => _adUnit != null;
  InterstitialAd? _adUnit;

  void adLoad() {
    if ((Platform.isAndroid || Platform.isIOS) == false) return;
    log("load InterstitialAd");

    InterstitialAd.load(
      adUnitId: unitId!,
      request: const AdRequest(),
      adLoadCallback: InterstitialAdLoadCallback(
        onAdLoaded: (InterstitialAd ad) {
          _adUnit = ad;
          log("InterstitialAd loaded");
          update();
        },
        onAdFailedToLoad: (LoadAdError error) {
          log("interstitial ad load: $error");
          _adUnit = null;
          update();
        },
      ),
    );
  }

  void adShow() {
    if (loaded == false) return;
    _adUnit?.show();
    _adUnit = null;
    Future.delayed(showDuration, () => adLoad());
    update();
  }
}
