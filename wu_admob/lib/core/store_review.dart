part of '../wu_admob.dart';

class StoreReview {
  static final StoreReview _instance = StoreReview._();
  StoreReview._();
  static StoreReview get I => _instance;

  final dateFormat = DateFormat("yyyy-MM-dd HH:mm:ss");

  int minRemindDays = 7;
  int minUseCoins = 5;
  DateTime? startDate;
  int useCoins = 0;

  bool get _isPlatformSupported => Platform.isAndroid || Platform.isIOS;

  Future<void> init({int minRemindDays = 7, int minUseCoins = 5}) async {
    if (!_isPlatformSupported) return;

    this.minRemindDays = minRemindDays;
    this.minUseCoins = minUseCoins;
    await GetStorage.init();
    load();
  }

  Future<void> useCoinsAdd() async {
    if (!_isPlatformSupported) return;
    useCoins++;
    await save(); // Await here to ensure data is saved before proceeding.
  }

  void load() {
    final box = GetStorage();
    final strStartDate = box.read<String?>('ReviewStartDate');
    useCoins = box.read<int?>('ReviewUseCoins') ?? 0;
    if (strStartDate != null) {
      try {
        startDate = dateFormat.parse(strStartDate);
      } catch (e) {
        // Handle potential parsing errors gracefully.
        startDate = DateTime.now();
        log("Error parsing ReviewStartDate: $e, resetting to now");
        save();
      }
    } else {
      startDate = DateTime.now();
      save();
    }
  }

  Future<void> save() async {
    final box = GetStorage();
    await box.write('ReviewStartDate', dateFormat.format(startDate!));
    await box.write('ReviewUseCoins', useCoins);
  }

  Future<void> show() async {
    if (!_isPlatformSupported) return;

    final InAppReview inAppReview = InAppReview.instance;

    if (await inAppReview.isAvailable()) {
      // Removed Future.delayed as it was not necessary and caused confusion.
      final now = DateTime.now();
      log("now: $now, startDate: $startDate, minRemindDays: $minRemindDays, minUseCoins: $minUseCoins, useCoins: $useCoins");
      if (now.difference(startDate ?? DateTime.now()).inDays >= minRemindDays && useCoins >= minUseCoins) {
        try {
          inAppReview.requestReview();
        } catch (e) {
          log("Error requesting review: $e");
        }
      }
    }
  }
}
