part of "../wu_admob.dart";

class BannerController extends GetxController {
  final String? iosAdId;
  final String? androidAdId;

  BannerController({this.androidAdId, this.iosAdId});

  @override
  void onInit() {
    super.onInit();
    adLoad();
  }

  BannerAd? _adUnit;
  bool get loaded => _adUnit != null;

  String? get unitId {
    try {
      return Platform.isAndroid ? androidAdId : iosAdId;
    } catch (error) {
      log("Banner error: $error");
      return null;
    }
  }

  void adLoad() {
    if ((Platform.isAndroid || Platform.isIOS) == false) return;
    log("load BannerAd");
    _adUnit = BannerAd(
        size: AdSize.banner,
        adUnitId: unitId!,
        listener: BannerAdListener(
          onAdFailedToLoad: (ad, error) {
            log("failed load BannerAd: $error");

            ad.dispose();
            _adUnit = null;
            update();
          },
          onAdLoaded: (ad) {
            update();
          },
        ),
        request: const AdRequest())
      ..load();
  }

  Widget get adWidget {
    if (Platform.isAndroid || Platform.isIOS) {
      return loaded
          ? Container(
              color: Colors.grey,
              height: _adUnit!.size.height.toDouble(),
              child: AdWidget(ad: _adUnit!),
            )
          : Container();
    }
    return const Placeholder(fallbackHeight: 50, fallbackWidth: double.infinity);
  }
}
