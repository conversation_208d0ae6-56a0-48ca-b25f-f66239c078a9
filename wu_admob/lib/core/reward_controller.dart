// ignore_for_file: constant_identifier_names

part of "../wu_admob.dart";

String dateString(DateTime datetime) {
  return DateFormat("yyyyMMdd").format(datetime);
}

//
const RewardTicketCount = "RewardTicketCount";

class RewardController extends GetxController {
  final String? iosAdId;
  final String? androidAdId;
  final Duration initDuration;
  final Duration watchDuration;
  final int initTickCount;

  String? get unitId {
    try {
      return Platform.isAndroid ? androidAdId : iosAdId;
    } catch (error) {
      return null;
    }
  }

  RewardController({
    this.androidAdId,
    this.iosAdId,
    this.initDuration = const Duration(seconds: 10),
    this.watchDuration = const Duration(minutes: 1),
    this.initTickCount = 0,
  }) {
    GetStorage.init().then((value) {
      loadWatchRecord();
      Timer(initDuration, () => _adLoad());
      timerStart();
    });
  }

  void timerStart() {
    Timer.periodic(watchDuration, (timer) {
      if (_adUnit != null) return;
      _adLoad();
    });
  }

  int ticketCount = 0;

  void loadWatchRecord() {
    GetStorage box = GetStorage();
    ticketCount = box.read(RewardTicketCount) ?? initTickCount;
    saveWatchRecord();
    update();
  }

  Future saveWatchRecord() async {
    GetStorage box = GetStorage();
    await box.write(RewardTicketCount, ticketCount);
    update();
  }

  Future ticketInc(int count) async {
    if ((Platform.isAndroid || Platform.isIOS) == false) return;
    ticketCount += count;
    await saveWatchRecord();
  }

  Future ticketDec(int count, {String errorMessage = "no coins available"}) async {
    if ((Platform.isAndroid || Platform.isIOS) == false) return;
    if (ticketCount >= count) {
      ticketCount -= count;
      await saveWatchRecord();
    } else {
      throw (errorMessage);
    }
  }

  RewardedAd? _adUnit;
  bool get loaded => _adUnit != null;

  void _adLoad() {
    if ((Platform.isAndroid || Platform.isIOS) == false) return;

    loadWatchRecord();
    log("before load: $unitId, $loaded");
    if (unitId == null) return;
    if (loaded == true) return;
    log("load reward ad");
    RewardedAd.load(
      adUnitId: unitId!,
      request: const AdRequest(),
      rewardedAdLoadCallback: RewardedAdLoadCallback(
        onAdLoaded: (RewardedAd ad) {
          _adUnit = ad;
          update();
        },
        onAdFailedToLoad: (LoadAdError error) {
          throw ("$error");
        },
      ),
    );
  }

  Future adShow() async {
    if (_adUnit == null) return;
    _adUnit!.setImmersiveMode(true);
    await _adUnit!.show(onUserEarnedReward: (AdWithoutView ad, RewardItem reward) {
      _adUnit = null;
      if (kDebugMode) {
        // debug only
        ticketInc(5);
      } else {
        ticketInc(1);
      }
      log("$ad with reward $RewardItem(${reward.amount}, ${reward.type})");
    });
    _adUnit = null;
    update();
  }
}
