part of "../wu_admob.dart";

/// 閱讀按鈕元件
/// 用於顯示需要消耗代幣的閱讀功能按鈕
///
/// 注意事項：
/// 1. 此元件已內建錯誤處理機制，會自動顯示錯誤提示 Snackbar
/// 2. 使用此元件時，請勿在 [onReadingTap] 回調中重複處理錯誤
/// 3. 如需自定義錯誤處理，請考慮自行實現類似功能而不使用此元件
///
/// 範例：
/// ```dart
/// ReadingButton(
///   decCoins: 10,
///   onReadingTap: () async {
///     // ✓ 正確用法：直接拋出異常，讓元件處理
///     if (condition) throw Exception('錯誤訊息');
///
///     // ✗ 錯誤用法：不要在這裡處理錯誤
///     // try {
///     //   ...
///     // } catch (e) {
///     //   showError(e);  // 會導致重複的錯誤提示
///     // }
///
///     return true;
///   },
/// )
/// ```
class ReadingButton extends StatelessWidget {
  final int decCoins; // 需要扣除的代幣數量
  final Future<bool> Function()? onReadingTap; // 點擊閱讀時的回調函數
  final List<Widget> children; // 子元件列表
  final String readingText; // 閱讀按鈕文字
  final Color? backColor; // 背景顏色
  final Color? foreColor; // 前景顏色

  const ReadingButton({
    super.key,
    required this.decCoins,
    this.onReadingTap,
    this.children = const [],
    this.readingText = "解讀",
    this.backColor,
    this.foreColor,
  });

  @override
  Widget build(BuildContext context) {
    return GetBuilder<RewardController>(builder: (rewardCtrl) {
      final adEnabled = (Platform.isAndroid || Platform.isIOS);
      return ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: backColor,
          foregroundColor: foreColor,
          disabledBackgroundColor: Colors.grey,
          disabledForegroundColor: Colors.black54,
        ),
        onPressed: _buildOnPressed(rewardCtrl, adEnabled),
        child: _buildButtonChild(rewardCtrl, adEnabled),
      );
    });
  }

  /// 建立按鈕點擊事件
  VoidCallback? _buildOnPressed(RewardController rewardCtrl, bool adEnabled) {
    if (rewardCtrl.ticketCount < decCoins) return null;

    return () async {
      _showLoadingDialog();
      await _handleReadingProcess(rewardCtrl, adEnabled);
    };
  }

  /// 顯示載入對話框
  void _showLoadingDialog() {
    Get.dialog(
      PopScope(
        canPop: false,
        child: AlertDialog(
          shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(8.0))),
          backgroundColor: Get.theme.colorScheme.tertiaryContainer,
          content: const SizedBox(
            height: 100,
            child: Center(
              child: CircularProgressIndicator.adaptive(),
            ),
          ),
        ),
      ),
      barrierDismissible: false,
    );
  }

  /// 處理閱讀流程
  Future<void> _handleReadingProcess(RewardController rewardCtrl, bool adEnabled) async {
    try {
      final success = await onReadingTap?.call() ?? false;
      if (adEnabled && success) {
        rewardCtrl.ticketDec(decCoins);
      }
      Get.back(); // 關閉載入對話框
    } catch (e) {
      await _handleError(e);
    }
  }

  /// 處理錯誤情況
  Future<void> _handleError(dynamic error) async {
    Get.back(); // 關閉載入對話框
    await Future.delayed(const Duration(milliseconds: 300));
    _showErrorSnackbar(error);
  }

  /// 顯示錯誤提示
  void _showErrorSnackbar(dynamic error) {
    Get.snackbar(
      '發生錯誤',
      error.toString(),
      icon: Icon(
        Icons.error_outline,
        color: Get.theme.colorScheme.onErrorContainer,
      ),
      backgroundColor: Get.theme.colorScheme.errorContainer,
      colorText: Get.theme.colorScheme.onErrorContainer,
      duration: const Duration(seconds: 5),
      snackPosition: SnackPosition.TOP,
    );
  }

  /// 建立按鈕子元件
  Widget _buildButtonChild(RewardController rewardCtrl, bool adEnabled) {
    if (!adEnabled) {
      return Text(readingText, style: const TextStyle(height: 1.2, fontSize: 16));
    }

    return Row(
      spacing: 2,
      children: [
        Text(
          "$readingText ${rewardCtrl.ticketCount}-$decCoins",
          style: const TextStyle(
            height: 1.2,
            fontSize: 16,
          ),
        ),
        AdmobAssets.imageAiCoin,
      ],
    );
  }
}
