part of "../wu_admob.dart";

class ChargeTile extends StatelessWidget {
  final String youhaveText;
  final String watchAdText;
  final EdgeInsetsGeometry? margin;
  const ChargeTile({
    super.key,
    this.youhaveText = "你有:",
    this.watchAdText = "看廣告獲得AI幣",
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      decoration: BoxDecoration(
        color: colorScheme.secondaryContainer,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: colorScheme.outline),
      ),
      clipBehavior: Clip.antiAlias,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      margin: margin,
      child: GetBuilder<RewardController>(builder: (rewardCtrl) {
        return Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              spacing: 2,
              children: [
                Text(
                  "$youhaveText ${rewardCtrl.ticketCount}",
                  style: TextStyle(
                    height: 1.2,
                    fontSize: 16,
                    color: colorScheme.onSecondaryContainer,
                  ),
                ),
                AdmobAssets.imageAiCoin
              ],
            ),
            Spacer(),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: colorScheme.primary,
                foregroundColor: colorScheme.onPrimary,
              ),
              onPressed: rewardCtrl.loaded ? () async => await rewardCtrl.adShow() : null,
              child: Text(watchAdText),
            ),
          ],
        );
      }),
    );
  }
}
