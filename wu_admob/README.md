## Add the Google Mobile Ads plugin as a dependency

To access the AdMob APIs from the AdMob inline ads project, add google_mobile_ads as a dependency to the pubspec.yaml file located at the root of the project.

pubspec.yaml

    ...
    environment:
    # TODO: Update the minimum sdk version to 2.12.0 to support null safety.
    sdk: ">=2.17.0 <3.0.0"

    dependencies:
    flutter:
        sdk: flutter
    google_fonts: ^3.0.1

    # TODO: Add google_mobile_ads as a dependency
    google_mobile_ads: ^1.2.0

## Update AndroidManifest.xml (Android)

1. Open the android/app/src/main/AndroidManifest.xml file in Android Studio.
2. Add your AdMob app ID by adding a <meta-data> tag with the name com.google.android.gms.ads.              APPLICATION_ID. For example, if your AdMob app ID is ca-app-pub-3940256099942544~3347511713, then you need to add the following lines to the AndroidManifest.xml file.

AndroidManifest.xml

    <manifest>
        ...
        <application>
        ...
            <meta-data
                android:name="com.google.android.gms.ads.APPLICATION_ID"
                android:value="ca-app-pub-3940256099942544~3347511713"/>
        </application>

    </manifest>

## Update Info.plist (iOS)
1. Open the ios/Runner/Info.plist file in Android Studio.
2. Add a GADApplicationIdentifier key with the string value of your AdMob app ID. For example, if your AdMob app ID is ca-app-pub-3940256099942544~1458002511, then you need to add the following lines to the Info.plist file.
ios/Runner/Info.plist

    ...
    <key>GADApplicationIdentifier</key>
    <string>ca-app-pub-3940256099942544~1458002511</string>
    ...