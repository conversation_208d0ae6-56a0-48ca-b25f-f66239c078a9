PODS:
  - device_info_plus (0.0.1):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - in_app_review (2.0.0):
    - FlutterMacOS
  - package_info_plus (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - screen_retriever_macos (0.0.1):
    - FlutterMacOS
  - share_plus (0.0.1):
    - FlutterMacOS
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - url_launcher_macos (0.0.1):
    - FlutterMacOS
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS
  - window_manager (0.5.0):
    - FlutterMacOS

DEPENDENCIES:
  - device_info_plus (from `Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - in_app_review (from `Flutter/ephemeral/.symlinks/plugins/in_app_review/macos`)
  - package_info_plus (from `Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - screen_retriever_macos (from `Flutter/ephemeral/.symlinks/plugins/screen_retriever_macos/macos`)
  - share_plus (from `Flutter/ephemeral/.symlinks/plugins/share_plus/macos`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)
  - webview_flutter_wkwebview (from `Flutter/ephemeral/.symlinks/plugins/webview_flutter_wkwebview/darwin`)
  - window_manager (from `Flutter/ephemeral/.symlinks/plugins/window_manager/macos`)

EXTERNAL SOURCES:
  device_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  in_app_review:
    :path: Flutter/ephemeral/.symlinks/plugins/in_app_review/macos
  package_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  screen_retriever_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/screen_retriever_macos/macos
  share_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/share_plus/macos
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos
  webview_flutter_wkwebview:
    :path: Flutter/ephemeral/.symlinks/plugins/webview_flutter_wkwebview/darwin
  window_manager:
    :path: Flutter/ephemeral/.symlinks/plugins/window_manager/macos

SPEC CHECKSUMS:
  device_info_plus: 4fb280989f669696856f8b129e4a5e3cd6c48f76
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  in_app_review: 0599bccaed5e02f6bed2b0d30d16f86b63ed8638
  package_info_plus: f0052d280d17aa382b932f399edf32507174e870
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  screen_retriever_macos: 452e51764a9e1cdb74b3c541238795849f21557f
  share_plus: 510bf0af1a42cd602274b4629920c9649c52f4cc
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  url_launcher_macos: 0fba8ddabfc33ce0a9afe7c5fef5aab3d8d2d673
  webview_flutter_wkwebview: 1821ceac936eba6f7984d89a9f3bcb4dea99ebb2
  window_manager: b729e31d38fb04905235df9ea896128991cad99e

PODFILE CHECKSUM: 236401fc2c932af29a9fcf0e97baeeb2d750d367

COCOAPODS: 1.16.2
